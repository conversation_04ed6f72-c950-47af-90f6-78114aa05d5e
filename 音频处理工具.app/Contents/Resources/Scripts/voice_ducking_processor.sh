#!/bin/bash

# 人声闪避处理核心脚本
# 由macOS应用调用

# 音量调节功能
process_volume_adjustment() {
    echo "=========================================="
    echo "音量调节处理"
    echo "=========================================="
    echo "输入文件：$BACKGROUND"
    echo "目标音量：${VOLUME_DB}dB"
    echo "输出文件：$OUTPUT"
    echo ""

    # 获取当前音量
    echo "分析当前音量..."
    CURRENT_VOLUME=$($FFMPEG_PATH -i "$BACKGROUND" -af "volumedetect" -vn -sn -dn -f null /dev/null 2>&1 | grep "mean_volume" | awk '{print $5}' | sed 's/dB//')

    if [ -z "$CURRENT_VOLUME" ]; then
        echo "警告：无法检测当前音量，使用默认处理"
        CURRENT_VOLUME="-20"
    fi

    echo "当前音量：${CURRENT_VOLUME}dB"
    echo "目标音量：${VOLUME_DB}dB"

    # 计算音量调节量
    VOLUME_CHANGE=$(echo "$VOLUME_DB - $CURRENT_VOLUME" | bc -l)
    echo "音量调节：${VOLUME_CHANGE}dB"
    echo ""

    echo "开始处理..."
    $FFMPEG_PATH -i "$BACKGROUND" \
        -af "volume=${VOLUME_CHANGE}dB" \
        -c:a libmp3lame \
        -b:a 192k \
        -y \
        "$OUTPUT" \
        -hide_banner -loglevel error

    if [ $? -eq 0 ] && [ -f "$OUTPUT" ]; then
        # 验证输出音量
        NEW_VOLUME=$($FFMPEG_PATH -i "$OUTPUT" -af "volumedetect" -vn -sn -dn -f null /dev/null 2>&1 | grep "mean_volume" | awk '{print $5}' | sed 's/dB//')
        FINAL_DURATION=$($FFPROBE_PATH -v quiet -show_entries format=duration -of csv=p=0 "$OUTPUT" 2>/dev/null)
        FINAL_SIZE=$(ls -lh "$OUTPUT" 2>/dev/null | awk '{print $5}')

        echo "处理完成！"
        echo "输出文件：$OUTPUT"
        echo "实际音量：${NEW_VOLUME}dB"
        echo "时长：$(printf "%.1f" $FINAL_DURATION)秒"
        echo "大小：$FINAL_SIZE"
    else
        echo "错误：音量调节失败"
        exit 1
    fi
}

# 循环延长功能
process_loop_extension() {
    echo "=========================================="
    echo "循环延长处理"
    echo "=========================================="
    echo "输入文件：$BACKGROUND"
    echo "目标时长：${LOOP_DURATION}秒"
    echo "输出文件：$OUTPUT"
    echo ""

    # 获取原始时长
    echo "分析原始文件..."
    ORIGINAL_DURATION=$($FFPROBE_PATH -v quiet -show_entries format=duration -of csv=p=0 "$BACKGROUND" 2>/dev/null)

    if [ -z "$ORIGINAL_DURATION" ]; then
        echo "错误：无法读取文件信息"
        exit 1
    fi

    echo "原始时长：$(printf "%.1f" $ORIGINAL_DURATION)秒 ($(printf "%.1f" $(echo "$ORIGINAL_DURATION/60" | bc -l))分钟)"
    echo "目标时长：${LOOP_DURATION}秒 ($(printf "%.1f" $(echo "$LOOP_DURATION/60" | bc -l))分钟)"

    # 检查是否需要循环
    if (( $(echo "$LOOP_DURATION <= $ORIGINAL_DURATION" | bc -l) )); then
        echo "目标时长不大于原始时长，直接截取..."
        $FFMPEG_PATH -i "$BACKGROUND" \
            -t "$LOOP_DURATION" \
            -c:a libmp3lame \
            -b:a 192k \
            -y \
            "$OUTPUT" \
            -hide_banner -loglevel error
    else
        # 计算需要循环的次数
        LOOP_COUNT=$(echo "scale=0; $LOOP_DURATION / $ORIGINAL_DURATION + 1" | bc -l)
        echo "需要循环：${LOOP_COUNT}次"
        echo ""

        echo "开始处理..."
        $FFMPEG_PATH -stream_loop $((LOOP_COUNT - 1)) -i "$BACKGROUND" \
            -t "$LOOP_DURATION" \
            -c:a libmp3lame \
            -b:a 192k \
            -y \
            "$OUTPUT" \
            -hide_banner -loglevel error
    fi

    if [ $? -eq 0 ] && [ -f "$OUTPUT" ]; then
        FINAL_DURATION=$($FFPROBE_PATH -v quiet -show_entries format=duration -of csv=p=0 "$OUTPUT" 2>/dev/null)
        FINAL_SIZE=$(ls -lh "$OUTPUT" 2>/dev/null | awk '{print $5}')

        echo "处理完成！"
        echo "输出文件：$OUTPUT"
        echo "实际时长：$(printf "%.1f" $FINAL_DURATION)秒 ($(printf "%.1f" $(echo "$FINAL_DURATION/60" | bc -l))分钟)"
        echo "大小：$FINAL_SIZE"
    else
        echo "错误：循环延长失败"
        exit 1
    fi
}

# 默认参数
THRESHOLD="0.02"
RATIO="6"
ATTACK="80"
RELEASE="1500"
MAKEUP="1"
KNEE="2.5"

# 新功能参数
VOLUME_DB=""
LOOP_DURATION=""
PROCESSING_MODE="ducking"  # ducking, volume, loop

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -v|--voice)
            VOICE="$2"
            shift 2
            ;;
        -b|--background)
            BACKGROUND="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT="$2"
            shift 2
            ;;
        --gentle)
            THRESHOLD="0.05"
            RATIO="3"
            ATTACK="100"
            RELEASE="2000"
            shift
            ;;
        --normal)
            THRESHOLD="0.02"
            RATIO="6"
            ATTACK="80"
            RELEASE="1500"
            shift
            ;;
        --aggressive)
            THRESHOLD="0.01"
            RATIO="10"
            ATTACK="50"
            RELEASE="1000"
            shift
            ;;
        --volume)
            PROCESSING_MODE="volume"
            VOLUME_DB="$2"
            shift 2
            ;;
        --loop)
            PROCESSING_MODE="loop"
            LOOP_DURATION="$2"
            shift 2
            ;;
        *)
            shift
            ;;
    esac
done

# 检查必需参数
if [ "$PROCESSING_MODE" = "ducking" ]; then
    if [ -z "$VOICE" ] || [ -z "$BACKGROUND" ]; then
        echo "错误：人声闪避模式需要人声和背景音乐文件"
        exit 1
    fi
elif [ "$PROCESSING_MODE" = "volume" ]; then
    if [ -z "$BACKGROUND" ] || [ -z "$VOLUME_DB" ]; then
        echo "错误：音量调节模式需要背景音乐文件和目标音量"
        exit 1
    fi
elif [ "$PROCESSING_MODE" = "loop" ]; then
    if [ -z "$BACKGROUND" ] || [ -z "$LOOP_DURATION" ]; then
        echo "错误：循环模式需要背景音乐文件和目标时长"
        exit 1
    fi
fi

# 检查文件是否存在
if [ -n "$VOICE" ] && [ ! -f "$VOICE" ]; then
    echo "错误：找不到人声文件 $VOICE"
    exit 1
fi

if [ ! -f "$BACKGROUND" ]; then
    echo "错误：找不到背景音乐文件 $BACKGROUND"
    exit 1
fi

# 生成输出文件名 - 优化版本
if [ -z "$OUTPUT" ]; then
    # 获取原始文件名，去除扩展名和之前的处理标记
    BACKGROUND_NAME=$(basename "$BACKGROUND" | sed 's/\.[^.]*$//')

    # 清理文件名：移除之前的处理标记和时间戳
    CLEAN_NAME=$(echo "$BACKGROUND_NAME" | sed -E 's/_音量-?[0-9.]+dB_[0-9]{8}_[0-9]{6}//g' | sed -E 's/_循环[0-9]+秒_[0-9]{8}_[0-9]{6}//g' | sed -E 's/_人声闪避版_[0-9]{8}_[0-9]{6}//g' | sed -E 's/_[0-9]{8}_[0-9]{6}$//g')

    # 生成新的文件名
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    if [ "$PROCESSING_MODE" = "ducking" ]; then
        OUTPUT="${CLEAN_NAME}_人声闪避版_${TIMESTAMP}.mp3"
    elif [ "$PROCESSING_MODE" = "volume" ]; then
        OUTPUT="${CLEAN_NAME}_音量${VOLUME_DB}dB_${TIMESTAMP}.mp3"
    elif [ "$PROCESSING_MODE" = "loop" ]; then
        # 转换秒数为更友好的格式
        if [ "$LOOP_DURATION" -ge 3600 ]; then
            HOURS=$((LOOP_DURATION / 3600))
            OUTPUT="${CLEAN_NAME}_循环${HOURS}小时_${TIMESTAMP}.mp3"
        elif [ "$LOOP_DURATION" -ge 60 ]; then
            MINUTES=$((LOOP_DURATION / 60))
            OUTPUT="${CLEAN_NAME}_循环${MINUTES}分钟_${TIMESTAMP}.mp3"
        else
            OUTPUT="${CLEAN_NAME}_循环${LOOP_DURATION}秒_${TIMESTAMP}.mp3"
        fi
    fi
fi

# 设置PATH环境变量，确保能找到FFmpeg
export PATH="/opt/homebrew/bin:/usr/local/bin:$PATH"

# 检查FFmpeg
FFMPEG_PATH=""
if command -v ffmpeg >/dev/null 2>&1; then
    FFMPEG_PATH="ffmpeg"
elif [ -f "/opt/homebrew/bin/ffmpeg" ]; then
    FFMPEG_PATH="/opt/homebrew/bin/ffmpeg"
elif [ -f "/usr/local/bin/ffmpeg" ]; then
    FFMPEG_PATH="/usr/local/bin/ffmpeg"
else
    echo "错误：未找到FFmpeg，请先安装FFmpeg"
    echo "安装方法：brew install ffmpeg"
    echo "当前PATH: $PATH"
    exit 1
fi

echo "使用FFmpeg路径: $FFMPEG_PATH"

# 设置ffprobe路径
FFPROBE_PATH="${FFMPEG_PATH%/*}/ffprobe"
if [ ! -f "$FFPROBE_PATH" ]; then
    FFPROBE_PATH="ffprobe"
fi

# 根据处理模式执行不同的处理逻辑
if [ "$PROCESSING_MODE" = "volume" ]; then
    process_volume_adjustment
    exit 0
elif [ "$PROCESSING_MODE" = "loop" ]; then
    process_loop_extension
    exit 0
fi

# 人声闪避模式的处理逻辑
# 获取文件时长
VOICE_DURATION=$($FFPROBE_PATH -v quiet -show_entries format=duration -of csv=p=0 "$VOICE" 2>/dev/null)
BACKGROUND_DURATION=$($FFPROBE_PATH -v quiet -show_entries format=duration -of csv=p=0 "$BACKGROUND" 2>/dev/null)

if [ -z "$VOICE_DURATION" ] || [ -z "$BACKGROUND_DURATION" ]; then
    echo "错误：无法读取文件信息，请检查文件是否损坏"
    exit 1
fi

echo "文件信息："
echo "- 人声时长：$(printf "%.1f" $VOICE_DURATION)秒"
echo "- 背景音乐时长：$(printf "%.1f" $BACKGROUND_DURATION)秒"
echo ""

# 第一步：处理有人声的部分
echo "第一步：处理有人声的部分..."
$FFMPEG_PATH -i "$BACKGROUND" -i "$VOICE" \
    -filter_complex "
    [0:a][1:a]sidechaincompress=
    threshold=${THRESHOLD}:
    ratio=${RATIO}:
    attack=${ATTACK}:
    release=${RELEASE}:
    makeup=${MAKEUP}:
    knee=${KNEE}:
    link=average:
    detection=rms
    [out]
    " \
    -map "[out]" \
    -t "$VOICE_DURATION" \
    -c:a libmp3lame \
    -b:a 192k \
    -y \
    "temp_ducked_part.mp3" \
    -hide_banner -loglevel error

if [ $? -ne 0 ]; then
    echo "错误：第一步处理失败"
    exit 1
fi

# 检查是否需要处理剩余部分
if (( $(echo "$BACKGROUND_DURATION > $VOICE_DURATION" | bc -l) )); then
    echo "第二步：提取剩余部分..."
    $FFMPEG_PATH -i "$BACKGROUND" \
        -ss "$VOICE_DURATION" \
        -c:a libmp3lame \
        -b:a 192k \
        -y \
        "temp_original_part.mp3" \
        -hide_banner -loglevel error

    if [ $? -ne 0 ]; then
        echo "错误：第二步处理失败"
        exit 1
    fi

    echo "第三步：合并两个部分..."
    $FFMPEG_PATH -i "temp_ducked_part.mp3" -i "temp_original_part.mp3" \
        -filter_complex "[0:a][1:a]concat=n=2:v=0:a=1[out]" \
        -map "[out]" \
        -c:a libmp3lame \
        -b:a 192k \
        -y \
        "$OUTPUT" \
        -hide_banner -loglevel error

    # 清理临时文件
    rm -f "temp_ducked_part.mp3" "temp_original_part.mp3"
else
    mv "temp_ducked_part.mp3" "$OUTPUT"
fi

# 检查处理结果
if [ $? -eq 0 ] && [ -f "$OUTPUT" ]; then
    FINAL_DURATION=$($FFPROBE_PATH -v quiet -show_entries format=duration -of csv=p=0 "$OUTPUT" 2>/dev/null)
    FINAL_SIZE=$(ls -lh "$OUTPUT" 2>/dev/null | awk '{print $5}')

    echo "处理完成！"
    echo "输出文件：$OUTPUT"
    echo "时长：$(printf "%.1f" $FINAL_DURATION)秒"
    echo "大小：$FINAL_SIZE"
else
    echo "错误：处理失败"
    exit 1
fi
