# 🎵 音频处理工具.app 开发文档

## 📱 应用架构
这是一个基于AppleScript的macOS原生应用，提供三种专业音频处理功能：

### 🎯 核心功能模块
1. **人声闪避** (Voice Ducking)
2. **音量调节** (Volume Adjustment) 
3. **循环延长** (Loop Extension)

### 🏗️ 技术架构
- **前端界面**: AppleScript (main.scpt)
- **核心处理**: Bash脚本 (voice_ducking_processor.sh)
- **音频引擎**: FFmpeg
- **数学计算**: bc

## 📂 文件结构
```
音频处理工具.app/
├── Contents/
│   ├── Info.plist              # 应用配置
│   ├── PkgInfo                 # 包信息
│   ├── MacOS/
│   │   └── applet              # 可执行文件
│   └── Resources/
│       ├── applet.icns         # 应用图标
│       ├── applet.rsrc         # 资源文件
│       ├── Documentation/      # 文档目录
│       │   └── README.md       # 本文档
│       └── Scripts/
│           ├── main.scpt       # 主界面脚本
│           └── voice_ducking_processor.sh  # 核心处理脚本
```

## 🎛️ 功能实现详解

### 1. 人声闪避 (Voice Ducking)
**技术原理**: 侧链压缩 (Sidechain Compression)
- 使用人声作为侧链信号控制背景音乐音量
- 实时检测人声强度，动态调整背景音乐
- 确保音量变化自然平滑

**处理模式**:
- 温和: threshold=0.05, ratio=3:1, attack=100ms, release=2000ms
- 标准: threshold=0.02, ratio=6:1, attack=80ms, release=1500ms  
- 强烈: threshold=0.01, ratio=10:1, attack=50ms, release=1000ms

**处理流程**:
1. 分析人声和背景音乐时长
2. 对有人声时间段应用闪避效果
3. 对无人声时间段保持原始背景音乐
4. 无缝合并输出完整文件

### 2. 音量调节 (Volume Adjustment)
**技术原理**: 音频增益控制
- 自动检测当前音频平均音量
- 计算到目标音量的调节量
- 应用线性音量变化

**默认设置**:
- 目标音量: -26.4dB (行业标准)
- 音频编码: MP3 192kbps
- 质量验证: 处理后验证实际音量

**FFmpeg命令**:
```bash
ffmpeg -i input.mp3 -af "volume=${VOLUME_CHANGE}dB" output.mp3
```

### 3. 循环延长 (Loop Extension)
**技术原理**: 流循环技术
- 使用FFmpeg的stream_loop功能
- 自动计算所需循环次数
- 精确控制输出时长

**处理逻辑**:
- 目标时长 ≤ 原始时长: 直接截取
- 目标时长 > 原始时长: 循环播放

**FFmpeg命令**:
```bash
ffmpeg -stream_loop N -i input.mp3 -t duration output.mp3
```

## 🔧 核心脚本参数

### voice_ducking_processor.sh 参数
```bash
# 人声闪避模式
--gentle|--normal|--aggressive -v voice_file -b background_file

# 音量调节模式  
--volume target_db -b audio_file

# 循环延长模式
--loop duration_seconds -b audio_file

# 通用参数
-o output_file  # 指定输出文件名
```

### 环境变量设置
```bash
export PATH="/opt/homebrew/bin:/usr/local/bin:$PATH"
FFMPEG_PATH="ffmpeg"
FFPROBE_PATH="ffprobe"
```

## 🎨 界面交互流程

### 启动流程
1. `on run` -> `showWelcomeDialog()`
2. 用户选择功能模式
3. 根据模式调用相应处理函数

### 拖拽处理
1. `on open droppedItems`
2. 检测文件数量
3. 单文件: 选择处理模式
4. 双文件: 默认人声闪避模式

### 处理执行
1. `executeProcessing()` 构建shell命令
2. 调用 `voice_ducking_processor.sh`
3. 显示处理结果或错误信息

## 📊 错误处理机制

### FFmpeg路径检测
```bash
# 多路径检测
if command -v ffmpeg >/dev/null 2>&1; then
    FFMPEG_PATH="ffmpeg"
elif [ -f "/opt/homebrew/bin/ffmpeg" ]; then
    FFMPEG_PATH="/opt/homebrew/bin/ffmpeg"
elif [ -f "/usr/local/bin/ffmpeg" ]; then
    FFMPEG_PATH="/usr/local/bin/ffmpeg"
```

### 文件验证
- 检查输入文件存在性
- 验证文件格式支持
- 确认输出目录权限

### 处理验证
- 检查FFmpeg返回码
- 验证输出文件生成
- 分析输出文件属性

## 🔄 版本历史

### v2.0 (2025-06-20)
- ✅ 新增音量调节功能
- ✅ 新增循环延长功能  
- ✅ 更新界面支持多模式选择
- ✅ 优化FFmpeg路径检测
- ✅ 完善错误处理机制

### v1.0 (2025-06-20)
- ✅ 实现人声闪避功能
- ✅ 支持三种闪避模式
- ✅ macOS原生界面
- ✅ 拖拽文件支持

## 🛠️ 开发维护

### 依赖要求
- macOS 10.10+
- FFmpeg (通过Homebrew安装)
- bc (系统自带)

### 测试方法
```bash
# 测试音量调节
./voice_ducking_processor.sh --volume -26.4 -b "test.mp3"

# 测试循环延长  
./voice_ducking_processor.sh --loop 1800 -b "test.mp3"

# 测试人声闪避
./voice_ducking_processor.sh --normal -v "voice.mp3" -b "music.mp3"
```

### 常见问题
1. **FFmpeg未找到**: 检查PATH设置和安装状态
2. **权限错误**: 确认文件读写权限
3. **内存不足**: 大文件处理需要足够内存
4. **格式不支持**: 确认输入文件格式

## 📝 代码规范

### AppleScript规范
- 使用清晰的函数命名
- 提供用户友好的错误提示
- 保持界面交互一致性

### Bash脚本规范
- 严格的错误检查 (`set -e`)
- 详细的日志输出
- 参数验证和默认值设置

### 文件命名规范
- 输出文件自动添加时间戳
- 根据处理模式生成描述性文件名
- 保持原文件扩展名兼容性

---

**维护者**: AI Assistant  
**最后更新**: 2025-06-20  
**文档版本**: 2.0
