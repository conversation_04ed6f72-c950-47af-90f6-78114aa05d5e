#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
文本处理器
功能：从剪贴板读取文本，进行特定的文本处理，然后复制回剪贴板
作者：Augment AI
日期：2025-06-15
"""

import re
import os
import sys
import subprocess
import platform
import logging
import time

def setup_logging():
    """设置简单的日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )
    return logging.getLogger("文本处理器")

# 初始化日志
logger = setup_logging()

def get_clipboard_content():
    """获取剪贴板内容"""
    if platform.system() == 'Darwin':  # macOS
        try:
            process = subprocess.Popen(['pbpaste'], stdout=subprocess.PIPE)
            clipboard_content, _ = process.communicate()
            return clipboard_content.decode('utf-8')
        except Exception as e:
            logger.error(f"无法读取剪贴板内容: {e}")
            show_notification("错误", f"无法读取剪贴板内容: {e}")
            sys.exit(1)
    else:
        logger.error("此脚本目前仅支持macOS系统")
        show_notification("错误", "此脚本目前仅支持macOS系统")
        sys.exit(1)

def set_clipboard_content(text):
    """设置剪贴板内容"""
    if platform.system() == 'Darwin':  # macOS
        try:
            process = subprocess.Popen(['pbcopy'], stdin=subprocess.PIPE)
            process.communicate(input=text.encode('utf-8'))
            return True
        except Exception as e:
            logger.error(f"无法设置剪贴板内容: {e}")
            show_notification("错误", f"无法设置剪贴板内容: {e}")
            return False
    else:
        logger.error("此脚本目前仅支持macOS系统")
        show_notification("错误", "此脚本目前仅支持macOS系统")
        return False

def show_notification(title, message):
    """显示系统通知或对话框"""
    if platform.system() == 'Darwin':  # macOS
        # 直接使用对话框确保用户能看到反馈
        show_dialog(title, message)

        # 同时尝试显示通知（如果权限允许）
        try:
            subprocess.run([
                'osascript',
                '-e',
                f'display notification "{message}" with title "{title}" sound name "Glass"'
            ], check=False)  # 不检查返回值，允许失败
            logger.info(f"通知已尝试显示: {title} - {message}")
        except Exception as e:
            logger.info(f"通知显示失败（正常）: {e}")

def show_dialog(title, message):
    """显示对话框"""
    try:
        subprocess.run([
            'osascript',
            '-e',
            f'display dialog "{message}" with title "{title}" buttons {{"确定"}} default button "确定"'
        ], check=True)
        logger.info(f"对话框已显示: {title} - {message}")
    except Exception as e:
        logger.error(f"对话框显示失败: {e}")
        # 最后的备用方案：在终端输出
        print(f"[{title}] {message}")

def process_text(text):
    """处理文本内容，返回处理后的文本和操作统计"""
    logger.info("开始处理文本")

    operations = []  # 记录执行的操作
    original_text = text

    # 规则1: "甜美的"转化为"甜美得"
    count_rule1 = text.count("甜美的")
    text = text.replace("甜美的", "甜美得")
    if count_rule1 > 0:
        operations.append(f"替换 {count_rule1} 处 '甜美的' → '甜美得'")
        logger.info(f"完成规则1: 替换了 {count_rule1} 处")

    # 规则2: "着你。" 替换句号为逗号，删除句号后的换行
    count_rule2 = len(re.findall(r'着你。\s*\n', text))
    text = re.sub(r'着你。\s*\n', '着你，', text)
    if count_rule2 > 0:
        operations.append(f"处理 {count_rule2} 处 '着你。[换行]' → '着你，'")
        logger.info(f"完成规则2: 处理了 {count_rule2} 处")

    # 规则3: 删除文本中多余的空行
    original_lines = len(original_text.split('\n'))
    # 将多个连续的空行（包含空白字符的行）替换为单个换行符
    text = re.sub(r'\n\s*\n+', '\n', text)
    # 删除开头和结尾的空白字符
    text = text.strip()
    final_lines = len(text.split('\n'))

    empty_lines_removed = original_lines - final_lines
    if empty_lines_removed > 0:
        operations.append(f"删除 {empty_lines_removed} 行多余空行")
        logger.info(f"完成规则3: 删除了 {empty_lines_removed} 行空行")

    return text, operations

def main():
    """主函数"""
    logger.info("文本处理器启动")

    # 获取剪贴板内容
    original_text = get_clipboard_content()

    if not original_text.strip():
        logger.warning("剪贴板内容为空")
        show_notification("文本处理完成", "剪贴板内容为空，无需处理")
        sys.exit(0)

    logger.info(f"获取到剪贴板内容，长度: {len(original_text)} 字符")

    try:
        # 处理文本
        processed_text, operations = process_text(original_text)

        # 将处理后的文本复制回剪贴板
        if set_clipboard_content(processed_text):
            logger.info("文本处理完成，已复制到剪贴板")

            # 生成操作报告
            if operations:
                operations_text = "；".join(operations)
                message = f"处理完成！{operations_text}"
            else:
                message = "处理完成！未发现需要处理的内容"

            show_notification("文本处理完成", message)
            # 短暂延迟确保通知显示
            time.sleep(0.5)
        else:
            logger.error("无法将处理后的文本复制到剪贴板")
            show_notification("处理失败", "无法将处理后的文本复制到剪贴板")
            time.sleep(0.5)

    except Exception as e:
        logger.error(f"文本处理失败: {str(e)}", exc_info=True)
        show_notification("处理失败", f"错误: {str(e)[:50]}...")

if __name__ == "__main__":
    main()
