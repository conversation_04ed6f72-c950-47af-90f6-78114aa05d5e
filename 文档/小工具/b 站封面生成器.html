<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频封面生成器</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js" integrity="sha512-WdC3iB2otakVTCzHuI12sPtnMh9etdImBdwA8EVOuH4LOnyVnmk_CRVeUxmP9qaD3AWjYfolT+1adKdpSAVjBw==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Serif SC', serif;
            background-color: #2c3e50;
            color: #ecf0f1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            min-height: 100vh;
            padding: 2rem;
            box-sizing: border-box;
            margin: 0;
        }

        .controls {
            max-width: 1920px;
            width: 100%;
            margin-bottom: 2rem;
            background-color: #34495e;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            border: 1px solid #4a627a;
            display: grid;
            grid-template-columns: 1fr 2fr 1fr 1fr 1fr;
            gap: 1rem;
        }

        .control-group {
            display: flex;
            flex-direction: column;
        }

        .control-group.scale-group {
            grid-column: span 1;
        }

        .control-group.wide {
            grid-column: span 1;
        }

        .control-group label {
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #bdc3c7;
            font-size: 0.9rem;
        }

        .control-group input[type="text"],
        .control-group input[type="file"],
        .control-group input[type="number"] {
            padding: 0.75rem;
            border: 1px solid #4a627a;
            background-color: #2c3e50;
            color: #ecf0f1;
            border-radius: 4px;
            font-family: 'Noto Serif SC', serif;
            font-size: 1rem;
            margin-bottom: 0.5rem; /* Space between inputs */
        }

        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 1rem;
        }

        .control-group input::placeholder {
            color: #7f8c8d;
        }

        .preset-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .preset-btn {
            padding: 0.4rem 0.8rem;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            font-family: 'Noto Serif SC', serif;
            transition: background-color 0.3s ease;
        }

        .preset-btn:hover {
            background-color: #2980b9;
        }

        .preset-btn.active {
            background-color: #e74c3c;
        }

        /* Responsive layout adjustments */
        @media (max-width: 1200px) {
            .controls {
                grid-template-columns: 1fr 1fr;
                gap: 1rem;
            }
            .control-group.scale-group {
                grid-column: span 2;
            }
        }

        @media (max-width: 768px) {
            .controls {
                grid-template-columns: 1fr;
            }
            .control-group.scale-group {
                grid-column: span 1;
            }
        }

        .cover-wrapper {
            max-width: 1920px;
            width: 100%;
            /* The shadow is now on the wrapper, so it won't be part of the screenshot */
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        #cover-container {
            position: relative;
            /* Use aspect-ratio to maintain 16:9, let width be 100% of its parent */
            width: 100%;
            aspect-ratio: 16 / 9;
            /* box-shadow: 0 10px 30px rgba(0,0,0,0.3); */ /* <-- REMOVED FROM HERE */
            overflow: hidden; /* Ensures nothing spills out */
            background-color: #000;
        }

        #cover-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: 1;
            /* Add a subtle dark overlay to the image to make text more readable */
            filter: brightness(0.8);
            /* Hide alt text when image is not loaded */
            font-size: 0;
            color: transparent;
        }

        /* Hide the image completely when src is empty */
        #cover-image[src=""], #cover-image:not([src]) {
            opacity: 0;
        }

        .text-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 2;
            color: white;
            text-align: center;
            width: 100%;
            padding: 40px;
            box-sizing: border-box;
            text-shadow: 0px 3px 10px rgba(0, 0, 0, 0.7);
        }

        .main-title {
            font-size: 12vw; /* Default viewport width for responsive font size */
            font-weight: 700;
            margin: 0;
            letter-spacing: 0.15em; /* Add generous spacing for a premium feel */
            padding-left: 0.15em; /* Optical adjustment for letter spacing */
        }

        .subtitle {
            font-size: 4.8vw; /* Default viewport width for responsive font size */
            font-weight: 400;
            margin-top: 2vh;
            letter-spacing: 0.1em;
            opacity: 0.85;
        }

        .english-subtitle {
            font-size: 2.4vw; /* Default viewport width for responsive font size */
            font-weight: 400;
            margin-top: 3vh;
            letter-spacing: 0.4em; /* Wider spacing for english text */
            text-transform: uppercase;
            opacity: 0.75;
        }

        #export-button {
            margin-top: 2rem;
            padding: 1rem 2rem;
            font-size: 1.2rem;
            font-family: 'Noto Serif SC', serif;
            background-color: #e74c3c;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        #export-button:hover {
            background-color: #c0392b;
            transform: translateY(-2px);
        }

        .storage-controls {
            margin-top: 1rem;
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .clear-storage-btn {
            padding: 0.5rem 1rem;
            background-color: #95a5a6;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            font-family: 'Noto Serif SC', serif;
            transition: background-color 0.3s ease;
        }

        .clear-storage-btn:hover {
            background-color: #7f8c8d;
        }

        .storage-info {
            font-size: 0.8rem;
            color: #bdc3c7;
            font-style: italic;
        }

        .image-controls {
            margin-top: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .image-filename {
            font-size: 0.8rem;
            color: #bdc3c7;
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .clear-image-btn {
            padding: 0.3rem 0.6rem;
            background-color: #e74c3c;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
            font-family: 'Noto Serif SC', serif;
            transition: background-color 0.3s ease;
        }

        .clear-image-btn:hover {
            background-color: #c0392b;
        }

        /* Full screen image display styles */
        .fullscreen-image-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background-color: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .fullscreen-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .close-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: rgba(255, 255, 255, 0.9);
            color: #333;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 24px;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: background-color 0.3s ease;
        }

        .close-button:hover {
            background-color: rgba(255, 255, 255, 1);
        }


        /* Define fixed sizes for high-resolution export */
        .export-mode {
            width: 1920px !important;
            height: 1080px !important;
        }

        /* Export mode font sizes will be set dynamically by JavaScript */


        /* Responsive scaling for smaller screens */
        @media (min-width: 1920px) {
            .main-title { font-size: 180px; }
            .subtitle { font-size: 72px; }
            .english-subtitle { font-size: 36px; }
        }
    </style>
</head>
<body>
    <div class="controls">
        <div class="control-group">
            <label for="image-upload">自定义背景图片</label>
            <input type="file" id="image-upload" accept="image/*">
            <div class="image-controls">
                <span id="image-filename" class="image-filename"></span>
                <button type="button" id="clear-image-btn" class="clear-image-btn" style="display: none;">清除图片</button>
            </div>
        </div>

        <div class="control-group scale-group">
            <label for="master-size-slider">整体缩放 (<span id="master-size-value">150%</span>)</label>
            <input type="range" id="master-size-slider" min="50" max="300" value="150" step="5">
            <div class="preset-buttons">
                <button class="preset-btn" data-scale="75">75%</button>
                <button class="preset-btn" data-scale="100">100%</button>
                <button class="preset-btn" data-scale="125">125%</button>
                <button class="preset-btn active" data-scale="150">150%</button>
                <button class="preset-btn" data-scale="175">175%</button>
                <button class="preset-btn" data-scale="200">200%</button>
            </div>
        </div>

        <div class="control-group wide">
            <label for="main-title-input">主标题</label>
            <input type="text" id="main-title-input" placeholder="催眠深睡">
            <input type="number" id="main-title-size" data-base-size="180" value="270" title="主标题字体大小(px)" min="20" max="500" step="5">
        </div>

        <div class="control-group wide">
            <label for="subtitle-input">副标题</label>
            <input type="text" id="subtitle-input" placeholder="渐进式放松">
            <input type="number" id="subtitle-size" data-base-size="72" value="108" title="副标题字体大小(px)" min="10" max="200" step="2">
        </div>

        <div class="control-group wide">
            <label for="english-subtitle-input">英文标题</label>
            <input type="text" id="english-subtitle-input" placeholder="SLEEPY SHEEP HYPNOSIS">
            <input type="number" id="english-subtitle-size" data-base-size="36" value="54" title="英文标题字体大小(px)" min="10" max="150" step="2">
        </div>
    </div>

    <div class="cover-wrapper">
        <div id="cover-container">
            <img id="cover-image" src="" alt="背景图片">
            <div class="text-overlay">
                <h2 class="main-title" id="main-title-output">催眠深睡</h2>
                <p class="subtitle" id="subtitle-output">渐进式放松</p>
                <p class="english-subtitle" id="english-subtitle-output">SLEEPY SHEEP HYPNOSIS</p>
            </div>
        </div>
    </div>

    <button id="export-button">查看合成图片</button>

    <div class="storage-controls">
        <button id="clear-storage-btn" class="clear-storage-btn">清除保存的信息</button>
        <span id="storage-info" class="storage-info">信息已自动保存</span>
    </div>

    <script>
        // --- LOCALSTORAGE FUNCTIONALITY ---
        const STORAGE_KEY = 'video-cover-generator-data';

        // 保存数据到 localStorage
        function saveToStorage() {
            const data = {
                mainTitle: mainTitleInput.value,
                subtitle: subtitleInput.value,
                englishTitle: englishSubtitleInput.value,
                mainTitleSize: mainTitleSizeInput.value,
                subtitleSize: subtitleSizeInput.value,
                englishTitleSize: englishSubtitleSizeInput.value,
                masterScale: masterSlider.value,
                imageFilename: document.getElementById('image-filename').textContent || '',
                lastSaved: new Date().toLocaleString('zh-CN')
            };

            try {
                localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
                updateStorageInfo(data.lastSaved);
            } catch (error) {
                console.warn('无法保存数据到本地存储:', error);
            }
        }

        // 从 localStorage 加载数据
        function loadFromStorage() {
            try {
                const savedData = localStorage.getItem(STORAGE_KEY);
                if (savedData) {
                    const data = JSON.parse(savedData);

                    // 恢复文本内容
                    if (data.mainTitle) {
                        mainTitleInput.value = data.mainTitle;
                        mainTitleOutput.textContent = data.mainTitle;
                    }
                    if (data.subtitle) {
                        subtitleInput.value = data.subtitle;
                        subtitleOutput.textContent = data.subtitle;
                    }
                    if (data.englishTitle) {
                        englishSubtitleInput.value = data.englishTitle;
                        englishSubtitleOutput.textContent = data.englishTitle.toUpperCase();
                    }

                    // 恢复字体大小
                    if (data.mainTitleSize) mainTitleSizeInput.value = data.mainTitleSize;
                    if (data.subtitleSize) subtitleSizeInput.value = data.subtitleSize;
                    if (data.englishTitleSize) englishSubtitleSizeInput.value = data.englishTitleSize;

                    // 恢复缩放比例
                    if (data.masterScale) {
                        masterSlider.value = data.masterScale;
                        masterValueSpan.textContent = `${data.masterScale}%`;
                        updateActivePresetButton(parseInt(data.masterScale));
                    }

                    // 恢复图片文件名显示（提示用户重新选择）
                    if (data.imageFilename) {
                        updateImageDisplay(data.imageFilename, true); // true 表示这是恢复的文件名
                    }

                    // 更新显示信息
                    if (data.lastSaved) {
                        updateStorageInfo(data.lastSaved);
                    }

                    return true;
                }
            } catch (error) {
                console.warn('无法从本地存储加载数据:', error);
            }
            return false;
        }

        // 清除保存的数据
        function clearStorage() {
            try {
                localStorage.removeItem(STORAGE_KEY);
                updateStorageInfo(null);

                // 重置所有输入框
                mainTitleInput.value = '';
                subtitleInput.value = '';
                englishSubtitleInput.value = '';

                // 重置显示内容为占位符
                mainTitleOutput.textContent = mainTitleInput.placeholder;
                subtitleOutput.textContent = subtitleInput.placeholder;
                englishSubtitleOutput.textContent = englishSubtitleInput.placeholder;

                // 清除图片
                clearImageData();

                // 重置字体大小为默认值
                mainTitleSizeInput.value = mainTitleSizeInput.dataset.baseSize;
                subtitleSizeInput.value = subtitleSizeInput.dataset.baseSize;
                englishSubtitleSizeInput.value = englishSubtitleSizeInput.dataset.baseSize;

                // 重置缩放为150%
                masterSlider.value = 150;
                masterSlider.dispatchEvent(new Event('input', { bubbles: true }));

                alert('已清除所有保存的信息');
            } catch (error) {
                console.warn('清除数据时出错:', error);
            }
        }

        // 更新存储信息显示
        function updateStorageInfo(lastSaved) {
            const storageInfo = document.getElementById('storage-info');
            if (lastSaved) {
                storageInfo.textContent = `上次保存: ${lastSaved}`;
            } else {
                storageInfo.textContent = '信息已自动保存';
            }
        }

        // 更新图片显示信息
        function updateImageDisplay(filename, isRestored = false) {
            const imageFilename = document.getElementById('image-filename');
            const clearImageBtn = document.getElementById('clear-image-btn');

            if (filename) {
                if (isRestored) {
                    imageFilename.innerHTML = `<span style="color: #f39c12;">📁 ${filename}</span> <small style="color: #95a5a6;">(需重新选择)</small>`;
                    clearImageBtn.style.display = 'block';
                } else {
                    imageFilename.innerHTML = `<span style="color: #27ae60;">✓ ${filename}</span>`;
                    clearImageBtn.style.display = 'block';
                }
            } else {
                imageFilename.textContent = '';
                clearImageBtn.style.display = 'none';
            }
        }

        // 清除图片数据
        function clearImageData() {
            coverImage.src = '';
            imageUpload.value = '';
            updateImageDisplay('');
        }

        // --- TEXT & IMAGE UPDATE LOGIC ---
        const mainTitleInput = document.getElementById('main-title-input');
        const subtitleInput = document.getElementById('subtitle-input');
        const englishSubtitleInput = document.getElementById('english-subtitle-input');
        const imageUpload = document.getElementById('image-upload');

        // Font size controls
        const mainTitleSizeInput = document.getElementById('main-title-size');
        const subtitleSizeInput = document.getElementById('subtitle-size');
        const englishSubtitleSizeInput = document.getElementById('english-subtitle-size');

        // Master slider
        const masterSlider = document.getElementById('master-size-slider');
        const masterValueSpan = document.getElementById('master-size-value');
        const sizeInputs = [mainTitleSizeInput, subtitleSizeInput, englishSubtitleSizeInput];
        const presetButtons = document.querySelectorAll('.preset-btn');

        const mainTitleOutput = document.getElementById('main-title-output');
        const subtitleOutput = document.getElementById('subtitle-output');
        const englishSubtitleOutput = document.getElementById('english-subtitle-output');
        const coverImage = document.getElementById('cover-image');

        mainTitleInput.addEventListener('input', () => {
            mainTitleOutput.textContent = mainTitleInput.value || mainTitleInput.placeholder;
            saveToStorage(); // 自动保存
        });

        subtitleInput.addEventListener('input', () => {
            subtitleOutput.textContent = subtitleInput.value || subtitleInput.placeholder;
            saveToStorage(); // 自动保存
        });

        englishSubtitleInput.addEventListener('input', () => {
            englishSubtitleOutput.textContent = englishSubtitleInput.value.toUpperCase() || englishSubtitleInput.placeholder;
            saveToStorage(); // 自动保存
        });

        // --- FONT SIZE SCALING LOGIC ---
        function updatePreviewFontSizes() {
            const previewWidth = document.getElementById('cover-container').offsetWidth;
            const exportWidth = 1920; // The width the font sizes are based on

            if (previewWidth === 0) return; // Avoid errors if the container isn't rendered yet

            const scaleFactor = previewWidth / exportWidth;

            // Update main title preview size
            const mainTitleBaseSize = mainTitleSizeInput.value;
            mainTitleOutput.style.fontSize = (mainTitleBaseSize * scaleFactor) + 'px';

            // Update subtitle preview size
            const subtitleBaseSize = subtitleSizeInput.value;
            subtitleOutput.style.fontSize = (subtitleBaseSize * scaleFactor) + 'px';

            // Update English subtitle preview size
            const englishSubtitleBaseSize = englishSubtitleSizeInput.value;
            englishSubtitleOutput.style.fontSize = (englishSubtitleBaseSize * scaleFactor) + 'px';
        }

        // Add event listeners to update font sizes as user types or resizes window
        mainTitleSizeInput.addEventListener('input', () => {
            updatePreviewFontSizes();
            saveToStorage(); // 自动保存
        });
        subtitleSizeInput.addEventListener('input', () => {
            updatePreviewFontSizes();
            saveToStorage(); // 自动保存
        });
        englishSubtitleSizeInput.addEventListener('input', () => {
            updatePreviewFontSizes();
            saveToStorage(); // 自动保存
        });
        window.addEventListener('resize', updatePreviewFontSizes);

        // When a user manually changes a size, update its base reference value.
        sizeInputs.forEach(input => {
            input.addEventListener('change', () => {
                const multiplier = parseInt(masterSlider.value) / 100;
                // Recalculate the base size based on the current value and slider position
                input.dataset.baseSize = Math.round(parseInt(input.value) / multiplier);
            });
        });

        // When the master slider is used, update all sizes based on their base values.
        masterSlider.addEventListener('input', () => {
            const multiplier = parseInt(masterSlider.value) / 100;
            masterValueSpan.textContent = `${Math.round(multiplier * 100)}%`;

            sizeInputs.forEach(input => {
                const baseSize = parseFloat(input.dataset.baseSize);
                input.value = Math.round(baseSize * multiplier);
                // Dispatch the 'input' event to trigger the live preview update
                input.dispatchEvent(new Event('input', { bubbles: true }));
            });

            // Update active preset button
            updateActivePresetButton(parseInt(masterSlider.value));
            saveToStorage(); // 自动保存
        });

        // Preset button functionality
        presetButtons.forEach(button => {
            button.addEventListener('click', () => {
                const scaleValue = parseInt(button.dataset.scale);
                masterSlider.value = scaleValue;
                masterSlider.dispatchEvent(new Event('input', { bubbles: true }));
            });
        });

        // Function to update active preset button
        function updateActivePresetButton(currentScale) {
            presetButtons.forEach(btn => {
                btn.classList.remove('active');
                if (parseInt(btn.dataset.scale) === currentScale) {
                    btn.classList.add('active');
                }
            });
        }

        // Initialize everything on page load
        window.addEventListener('load', () => {
            // 尝试从本地存储加载数据
            const hasStoredData = loadFromStorage();

            // 如果没有存储的数据，使用默认占位符
            if (!hasStoredData) {
                mainTitleOutput.textContent = mainTitleInput.placeholder;
                subtitleOutput.textContent = subtitleInput.placeholder;
                englishSubtitleOutput.textContent = englishSubtitleInput.placeholder;
            }

            // Set initial font sizes, scaled for the preview
            updatePreviewFontSizes();
        });

        imageUpload.addEventListener('change', (event) => {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    coverImage.src = e.target.result;
                    updateImageDisplay(file.name, false); // false 表示这是新选择的文件
                    saveToStorage(); // 自动保存
                };
                reader.readAsDataURL(file);
            } else {
                clearImageData();
                saveToStorage(); // 自动保存
            }
        });

        // 清除存储按钮事件监听器
        document.getElementById('clear-storage-btn').addEventListener('click', () => {
            if (confirm('确定要清除所有保存的信息吗？此操作无法撤销。')) {
                clearStorage();
            }
        });

        // 清除图片按钮事件监听器
        document.getElementById('clear-image-btn').addEventListener('click', () => {
            clearImageData();
            saveToStorage(); // 自动保存
        });


        // --- FULLSCREEN IMAGE DISPLAY LOGIC ---
        function showFullscreenImage(imageDataUrl) {
            // Create fullscreen container
            const fullscreenContainer = document.createElement('div');
            fullscreenContainer.className = 'fullscreen-image-container';

            // Create image element
            const img = document.createElement('img');
            img.src = imageDataUrl;
            img.className = 'fullscreen-image';
            img.alt = '合成的封面图片';

            // Create close button
            const closeButton = document.createElement('button');
            closeButton.className = 'close-button';
            closeButton.innerHTML = '×';
            closeButton.title = '关闭';

            // Add elements to container
            fullscreenContainer.appendChild(img);
            fullscreenContainer.appendChild(closeButton);

            // Add to body
            document.body.appendChild(fullscreenContainer);

            // Close functionality
            const closeFullscreen = () => {
                document.body.removeChild(fullscreenContainer);
            };

            closeButton.addEventListener('click', closeFullscreen);

            // Close on ESC key
            const handleKeyPress = (e) => {
                if (e.key === 'Escape') {
                    closeFullscreen();
                    document.removeEventListener('keydown', handleKeyPress);
                }
            };
            document.addEventListener('keydown', handleKeyPress);

            // Close on background click
            fullscreenContainer.addEventListener('click', (e) => {
                if (e.target === fullscreenContainer) {
                    closeFullscreen();
                    document.removeEventListener('keydown', handleKeyPress);
                }
            });
        }

        // --- EXPORT LOGIC ---
        document.getElementById('export-button').addEventListener('click', function() {
            const container = document.getElementById('cover-container');
            const button = this;
            const controls = document.querySelector('.controls');
            const storageControls = document.querySelector('.storage-controls');
            const originalBodyStyle = document.body.style.cssText;

            // Hide controls and remove body padding for a clean capture
            button.style.display = 'none';
            controls.style.display = 'none';
            storageControls.style.display = 'none';
            document.body.style.padding = '0';
            document.body.style.margin = '0';

            // Add a class to force high resolution dimensions for the capture
            container.classList.add('export-mode');

            // Ensure custom font sizes are set to their base values for high-res export
            mainTitleOutput.style.fontSize = mainTitleSizeInput.value + 'px';
            subtitleOutput.style.fontSize = subtitleSizeInput.value + 'px';
            englishSubtitleOutput.style.fontSize = englishSubtitleSizeInput.value + 'px';

            // Use a short timeout to allow the browser to re-render the layout changes before capturing
            setTimeout(() => {
                html2canvas(container, {
                    scale: 2, // Capture at 2x resolution (3840x2160) for maximum quality
                    useCORS: true, // Needed for external images/fonts
                    backgroundColor: null, // Use transparent background
                    onclone: (doc) => {
                        const coverImageClone = doc.getElementById('cover-image');
                        coverImageClone.src = coverImage.src; // Ensure the custom image is used in the clone

                        // Apply custom font sizes to the cloned elements
                        doc.getElementById('main-title-output').style.fontSize = mainTitleSizeInput.value + 'px';
                        doc.getElementById('subtitle-output').style.fontSize = subtitleSizeInput.value + 'px';
                        doc.getElementById('english-subtitle-output').style.fontSize = englishSubtitleSizeInput.value + 'px';

                        // This ensures the web font is loaded in the cloned document
                        const link = doc.createElement('link');
                        link.rel = 'stylesheet';
                        link.href = 'https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;700&display=swap';
                        doc.head.appendChild(link);
                    }
                }).then(canvas => {
                    // Show the image in fullscreen mode on the current page
                    const imageDataUrl = canvas.toDataURL('image/png');
                    showFullscreenImage(imageDataUrl);

                    // Restore visibility and styles on the original page
                    container.classList.remove('export-mode');
                    button.style.display = 'block';
                    controls.style.display = 'block';
                    storageControls.style.display = 'flex';
                    document.body.style.cssText = originalBodyStyle;

                    // Rescale fonts for the preview after exporting
                    updatePreviewFontSizes();
                });
            }, 100); // 100ms delay to ensure styles are applied
        });
    </script>

</body>
</html>