# 🎬 视频控制浏览器扩展

一个Chrome/Edge浏览器扩展，让您在Notion页面工作时能够直接控制同窗口内哔哩哔哩和YouTube视频的播放。

## ✨ 功能特点

- 🎯 **跨页面控制**：在Notion页面直接控制其他标签页的视频
- ⌨️ **快捷键操作**：简单的单键控制，操作便捷
- 🎛️ **自定义按键**：支持自定义按键映射，满足个人使用习惯
- 🚫 **字符拦截**：在Notion编辑时不会误输入字符
- 🎨 **美观提示**：优雅的Toast通知，不干扰工作
- 🔧 **功能开关**：可随时启用/禁用扩展功能

## 🎮 快捷键说明

### 默认按键配置

| 按键 | 功能 |
|------|------|
| ``` (反引号) | 开启/关闭插件功能 |
| `1` | 视频后退5秒 |
| `2` | 播放/暂停视频 |
| `3` | 按下前进5秒，**按住**进入3倍速播放，**松开**恢复原速 |
| `4` | Notion: 创建行内代码 (Cmd/Ctrl+E) |
| `5` | Notion: 添加评论 (Shift+Cmd/Ctrl+M) |

### 🎛️ 自定义按键

- 点击扩展图标，选择"自定义按键"进入设置界面
- 可以为每个功能重新分配按键
- 支持按键冲突检测，确保每个按键只对应一个功能
- 可以禁用不需要的功能
- 支持一键恢复默认设置

## 🌐 支持的网站

- **哔哩哔哩** (bilibili.com)
- **YouTube** (youtube.com)
- **Notion** (notion.so) - 作为控制端

## 📦 安装方法

### 开发者模式安装

1. **下载扩展文件**
   - 将所有扩展文件下载到本地文件夹

2. **打开浏览器扩展管理页面**
   - Chrome: 访问 `chrome://extensions/`
   - Edge: 访问 `edge://extensions/`

3. **启用开发者模式**
   - 在扩展管理页面右上角打开"开发者模式"开关

4. **加载扩展**
   - 点击"加载已解压的扩展程序"
   - 选择包含扩展文件的文件夹
   - 扩展将自动安装并启用

## 🚀 使用方法

1. **打开视频网站**
   - 在浏览器中打开哔哩哔哩或YouTube
   - 开始播放视频

2. **打开Notion页面**
   - 在同一浏览器窗口的新标签页中打开Notion
   - 开始您的工作

3. **使用快捷键控制**
   - 按 ``` 键确保扩展已启用
   - 使用 `1`、`2`、`3` 键控制视频播放
   - 无需切换标签页！

## ⚙️ 扩展设置

- 点击浏览器工具栏中的扩展图标打开控制面板
- 可以查看当前状态和快捷键说明
- 可以通过按钮或快捷键切换功能开关
- **自定义按键设置**：
  - 点击"自定义按键"按钮进入设置界面
  - 为每个功能分配您喜欢的按键
  - 实时预览按键映射效果
  - 支持导出/导入按键配置（计划功能）

## 🔧 技术实现

- **Manifest V3**：使用最新的扩展标准
- **Content Scripts**：注入到页面的脚本处理快捷键
- **Background Script**：处理标签页间的消息传递
- **Storage API**：保存用户设置
- **现代UI**：美观的弹窗界面和Toast通知

## 📁 文件结构

```
video-control-extension/
├── manifest.json          # 扩展配置文件
├── background.js           # 后台脚本
├── content.js             # 内容脚本
├── styles.css             # Toast样式
├── popup.html             # 弹窗界面
├── popup.css              # 弹窗样式
├── popup.js               # 弹窗逻辑
├── README.md              # 说明文档
└── icons/                 # 图标文件
    ├── icon16.png
    ├── icon32.png
    ├── icon48.png
    └── icon128.png
```

## 🎯 使用场景

- 📝 在Notion做笔记时观看教学视频
- 📚 学习过程中需要暂停、重听某段内容
- 💼 工作中同时处理文档和视频会议
- 🎓 在线课程学习，边做笔记边控制视频

## 🔍 故障排除

**快捷键不起作用？**
- 确保扩展已启用（按 ``` 键检查）
- 确保焦点不在输入框内
- 刷新页面重新加载扩展

**找不到视频？**
- 确保视频和Notion在同一浏览器窗口
- 检查视频网站是否被支持
- 确保视频已开始播放

**Toast通知不显示？**
- 检查浏览器是否阻止了弹窗
- 确保扩展有足够权限

## 📝 更新日志

### v3.1 (当前版本)
- 🎛️ **自定义按键功能**:
  - 新增自定义按键映射功能，用户可以自由分配按键
  - 支持按键冲突检测和错误提示
  - 提供直观的设置界面，实时预览按键配置
  - 支持一键恢复默认设置
  - 按键映射实时同步到所有标签页
- 🔧 **代码架构优化**:
  - 重构按键处理逻辑，支持动态按键映射
  - 改进存储管理，支持自定义配置持久化
  - 优化UI界面，新增滑动面板设计

### v3.0
- ✨ **Notion 功能增强**:
  - 添加快捷键 `4` 用于在 Notion 中创建行内代码 (模拟 `Cmd/Ctrl+E`)
  - 添加快捷键 `5` 用于在 Notion 中添加评论 (模拟 `Shift+Cmd/Ctrl+M`)
- 🧠 **智能视频控制优化**:
  - 优先控制用户最后交互或正在播放声音的视频标签页，解决多视频控制冲突问题。
- ▶️ **快捷键 `3` 功能优化**:
  - 当视频暂停时，长按 `3` 键会自动播放视频并进入3倍速播放，松开恢复原速并保持播放。

### v1.1
- 🎉 首次发布
- ✅ 支持哔哩哔哩和YouTube
- ✅ 实现跨页面视频控制
- ✅ 优雅的Toast通知系统
- ✅ 现代化弹窗界面

## 📄 许可证

此项目仅供个人学习和使用。

---

💡 **提示**：如果您觉得这个扩展有用，请给个星标⭐支持一下！