// 弹窗界面的逻辑处理

// 默认按键映射
const defaultKeyMappings = {
  '`': 'toggleExtension',
  '1': 'rewind',
  '2': 'toggle',
  '3': 'forwardOrSpeed',
  '4': 'notionInlineCode',
  '5': 'notionAddComment'
};

// 动作名称映射
const actionNames = {
  'toggleExtension': '切换扩展',
  'rewind': '视频后退5秒',
  'toggle': '播放/暂停',
  'forwardOrSpeed': '前进/倍速',
  'notionInlineCode': 'Notion行内代码',
  'notionAddComment': 'Notion添加评论',
  'disabled': '禁用'
};

document.addEventListener('DOMContentLoaded', function() {
  const extensionStatusElement = document.getElementById('extensionStatus');
  const toggleButton = document.getElementById('toggleExtension');
  const toggleText = document.getElementById('toggleText');
  const customizeButton = document.getElementById('customizeKeys');
  const closeCustomizeButton = document.getElementById('closeCustomize');
  const saveCustomKeysButton = document.getElementById('saveCustomKeys');
  const resetToDefaultButton = document.getElementById('resetToDefault');

  // 加载并显示当前扩展状态
  loadExtensionStatus();

  // 加载并显示当前按键映射
  loadAndDisplayKeyMappings();

  // 绑定切换按钮事件
  toggleButton.addEventListener('click', toggleExtensionStatus);

  // 绑定自定义按键相关事件
  customizeButton.addEventListener('click', showCustomizePanel);
  closeCustomizeButton.addEventListener('click', hideCustomizePanel);
  saveCustomKeysButton.addEventListener('click', saveCustomKeyMappings);
  resetToDefaultButton.addEventListener('click', resetToDefaultMappings);

  // 加载扩展状态
  function loadExtensionStatus() {
    chrome.storage.local.get(['extensionEnabled'], (result) => {
      const isEnabled = result.extensionEnabled !== false; // 默认启用
      updateUI(isEnabled);
    });
  }

  // 加载并显示按键映射
  function loadAndDisplayKeyMappings() {
    chrome.storage.local.get(['customKeyMappings'], (result) => {
      const mappings = result.customKeyMappings || defaultKeyMappings;
      updateShortcutDisplay(mappings);
    });
  }

  // 更新界面显示
  function updateUI(isEnabled) {
    if (isEnabled) {
      extensionStatusElement.textContent = '已启用';
      extensionStatusElement.className = 'status-value';
      toggleText.textContent = '禁用扩展';
      toggleButton.className = 'control-btn primary';
    } else {
      extensionStatusElement.textContent = '已禁用';
      extensionStatusElement.className = 'status-value disabled';
      toggleText.textContent = '启用扩展';
      toggleButton.className = 'control-btn disabled';
    }
  }

  // 切换扩展状态
  function toggleExtensionStatus() {
    chrome.storage.local.get(['extensionEnabled'], (result) => {
      const currentState = result.extensionEnabled !== false;
      const newState = !currentState;

      // 保存新状态
      chrome.storage.local.set({ extensionEnabled: newState }, () => {
        updateUI(newState);

        // 通知所有标签页状态变化
        notifyAllTabs(newState);

        // 关闭弹窗
        setTimeout(() => {
          window.close();
        }, 500);
      });
    });
  }

  // 通知所有标签页扩展状态变化
  function notifyAllTabs(isEnabled) {
    chrome.tabs.query({}, (tabs) => {
      const message = isEnabled ? '视频控制扩展已启用' : '视频控制扩展已禁用';

      tabs.forEach(tab => {
        // 只向支持的网站发送消息
        if (tab.url && (
          tab.url.includes('notion.so') ||
          tab.url.includes('bilibili.com') ||
          tab.url.includes('youtube.com')
        )) {
          chrome.tabs.sendMessage(tab.id, {
            type: 'EXTENSION_STATUS_CHANGED',
            enabled: isEnabled,
            message: message
          }).catch(() => {
            // 忽略无法发送消息的标签页
          });
        }
      });
    });
  }
});

// 监听存储变化，实时更新UI
chrome.storage.onChanged.addListener((changes, namespace) => {
  if (namespace === 'local' && changes.extensionEnabled) {
    const extensionStatusElement = document.getElementById('extensionStatus');
    const toggleButton = document.getElementById('toggleExtension');
    const toggleText = document.getElementById('toggleText');

    if (extensionStatusElement && toggleButton && toggleText) {
      updateUI(changes.extensionEnabled.newValue);
    }
  }
});

// 更新UI的辅助函数
function updateUI(isEnabled) {
  const extensionStatusElement = document.getElementById('extensionStatus');
  const toggleButton = document.getElementById('toggleExtension');
  const toggleText = document.getElementById('toggleText');

  if (isEnabled) {
    extensionStatusElement.textContent = '已启用';
    extensionStatusElement.className = 'status-value';
    toggleText.textContent = '禁用扩展';
    toggleButton.className = 'control-btn primary';
  } else {
    extensionStatusElement.textContent = '已禁用';
    extensionStatusElement.className = 'status-value disabled';
    toggleText.textContent = '启用扩展';
    toggleButton.className = 'control-btn disabled';
  }
}

// 显示自定义按键面板
function showCustomizePanel() {
  const customizePanel = document.getElementById('customizePanel');
  customizePanel.classList.remove('hidden');
  loadCustomKeyMappings();
}

// 隐藏自定义按键面板
function hideCustomizePanel() {
  const customizePanel = document.getElementById('customizePanel');
  customizePanel.classList.add('hidden');
}

// 加载自定义按键映射到UI
function loadCustomKeyMappings() {
  chrome.storage.local.get(['customKeyMappings'], (result) => {
    const mappings = result.customKeyMappings || defaultKeyMappings;

    // 反向映射：从动作找到对应的按键
    const reverseMapping = {};
    for (const [key, action] of Object.entries(mappings)) {
      reverseMapping[action] = key;
    }

    // 填充UI
    const actions = ['toggleExtension', 'rewind', 'toggle', 'forwardOrSpeed', 'notionInlineCode', 'notionAddComment'];
    actions.forEach(action => {
      const keyInput = document.getElementById(`key-${action}`);
      const actionSelect = document.getElementById(`action-${action}`);

      if (keyInput && actionSelect) {
        keyInput.value = reverseMapping[action] || '';
        actionSelect.value = action;
      }
    });
  });
}

// 保存自定义按键映射
function saveCustomKeyMappings() {
  const newMappings = {};
  const usedKeys = new Set();
  const errors = [];

  // 收集所有映射
  const actions = ['toggleExtension', 'rewind', 'toggle', 'forwardOrSpeed', 'notionInlineCode', 'notionAddComment'];
  actions.forEach(action => {
    const keyInput = document.getElementById(`key-${action}`);
    const actionSelect = document.getElementById(`action-${action}`);

    if (keyInput && actionSelect) {
      const key = keyInput.value.trim();
      const selectedAction = actionSelect.value;

      // 清除之前的错误状态
      keyInput.classList.remove('error');

      if (key && selectedAction !== 'disabled') {
        // 检查按键冲突
        if (usedKeys.has(key)) {
          errors.push(`按键 "${key}" 被重复使用`);
          keyInput.classList.add('error');
        } else {
          usedKeys.add(key);
          newMappings[key] = selectedAction;
        }
      }
    }
  });

  // 如果有错误，显示错误信息
  if (errors.length > 0) {
    alert('保存失败：\n' + errors.join('\n'));
    return;
  }

  // 确保切换扩展的按键存在
  const toggleKey = Object.keys(newMappings).find(key => newMappings[key] === 'toggleExtension');
  if (!toggleKey) {
    alert('保存失败：必须为"切换扩展"功能分配一个按键');
    return;
  }

  // 保存到存储
  chrome.storage.local.set({ customKeyMappings: newMappings }, () => {
    // 更新快捷键说明
    updateShortcutDisplay(newMappings);

    // 通知所有标签页按键映射已更改
    notifyAllTabsKeyMappingChanged(newMappings);

    // 显示成功消息并关闭面板
    alert('按键设置已保存！');
    hideCustomizePanel();
  });
}

// 重置为默认按键映射
function resetToDefaultMappings() {
  if (confirm('确定要重置为默认按键设置吗？')) {
    chrome.storage.local.set({ customKeyMappings: defaultKeyMappings }, () => {
      loadCustomKeyMappings();
      updateShortcutDisplay(defaultKeyMappings);
      notifyAllTabsKeyMappingChanged(defaultKeyMappings);
      alert('已重置为默认按键设置！');
    });
  }
}

// 更新快捷键说明显示
function updateShortcutDisplay(mappings) {
  const shortcutsList = document.querySelector('.shortcuts-list');
  if (!shortcutsList) return;

  // 清空现有内容
  shortcutsList.innerHTML = '';

  // 重新生成快捷键列表
  for (const [key, action] of Object.entries(mappings)) {
    const shortcutItem = document.createElement('div');
    shortcutItem.className = 'shortcut-item';

    const keySpan = document.createElement('span');
    keySpan.className = action.includes('notion') ? 'key notion-key' : 'key';
    keySpan.textContent = key;

    const descSpan = document.createElement('span');
    descSpan.className = 'description';
    descSpan.textContent = actionNames[action] || action;

    shortcutItem.appendChild(keySpan);
    shortcutItem.appendChild(descSpan);
    shortcutsList.appendChild(shortcutItem);
  }
}

// 通知所有标签页按键映射已更改
function notifyAllTabsKeyMappingChanged(newMappings) {
  chrome.tabs.query({}, (tabs) => {
    tabs.forEach(tab => {
      if (tab.url && (
        tab.url.includes('notion.so') ||
        tab.url.includes('bilibili.com') ||
        tab.url.includes('youtube.com')
      )) {
        chrome.tabs.sendMessage(tab.id, {
          type: 'KEY_MAPPING_CHANGED',
          mappings: newMappings
        }).catch(() => {
          // 忽略无法发送消息的标签页
        });
      }
    });
  });
}