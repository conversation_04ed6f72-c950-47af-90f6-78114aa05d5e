<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>视频控制扩展</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="popup-container">
    <div class="header">
      <img src="icons/icon32.png" alt="图标" class="icon">
      <h1 class="title">视频控制扩展</h1>
    </div>

    <div class="status-section">
      <div class="status-item">
        <span class="status-label">扩展状态:</span>
        <span id="extensionStatus" class="status-value">已启用</span>
      </div>
    </div>

    <div class="shortcuts-section">
      <h2 class="section-title">快捷键说明</h2>
      <div class="shortcuts-list">
        <div class="shortcut-item">
          <span class="key">` (反引号)</span>
          <span class="description">开启/关闭扩展功能</span>
        </div>
        <div class="shortcut-item">
          <span class="key">1</span>
          <span class="description">视频后退5秒</span>
        </div>
        <div class="shortcut-item">
          <span class="key">2</span>
          <span class="description">播放/暂停视频</span>
        </div>
        <div class="shortcut-item">
          <span class="key">3</span>
          <span class="description">按下前进5秒，按住3倍速播放</span>
        </div>
        <div class="shortcut-item">
          <span class="key notion-key">4</span>
          <span class="description">Notion: 创建行内代码 (Cmd/Ctrl+E)</span>
        </div>
        <div class="shortcut-item">
          <span class="key notion-key">5</span>
          <span class="description">Notion: 添加评论 (Shift+Cmd/Ctrl+M)</span>
        </div>
      </div>
    </div>

    <div class="supported-sites">
      <h2 class="section-title">主要支持场景</h2>
      <div class="sites-list">
        <span class="site-badge">哔哩哔哩</span>
        <span class="site-badge">YouTube</span>
        <span class="site-badge">Notion (同窗口视频控制)</span>
      </div>
    </div>

    <div class="controls">
      <button id="toggleExtension" class="control-btn primary">
        <span id="toggleText">禁用扩展</span>
      </button>
      <button id="customizeKeys" class="control-btn secondary">
        <span>自定义按键</span>
      </button>
    </div>

    <!-- 自定义按键设置面板 -->
    <div id="customizePanel" class="customize-panel hidden">
      <div class="panel-header">
        <h2 class="section-title">自定义按键设置</h2>
        <button id="closeCustomize" class="close-btn">×</button>
      </div>

      <div class="key-mappings">
        <div class="key-mapping-item">
          <label class="mapping-label">切换扩展:</label>
          <input type="text" id="key-toggleExtension" class="key-input" maxlength="1" placeholder="按键">
          <select id="action-toggleExtension" class="action-select" disabled>
            <option value="toggleExtension">切换扩展</option>
          </select>
        </div>

        <div class="key-mapping-item">
          <label class="mapping-label">视频后退:</label>
          <input type="text" id="key-rewind" class="key-input" maxlength="1" placeholder="按键">
          <select id="action-rewind" class="action-select">
            <option value="rewind">视频后退5秒</option>
            <option value="toggle">播放/暂停</option>
            <option value="forwardOrSpeed">前进/倍速</option>
            <option value="notionInlineCode">Notion行内代码</option>
            <option value="notionAddComment">Notion添加评论</option>
            <option value="disabled">禁用</option>
          </select>
        </div>

        <div class="key-mapping-item">
          <label class="mapping-label">播放/暂停:</label>
          <input type="text" id="key-toggle" class="key-input" maxlength="1" placeholder="按键">
          <select id="action-toggle" class="action-select">
            <option value="toggle">播放/暂停</option>
            <option value="rewind">视频后退5秒</option>
            <option value="forwardOrSpeed">前进/倍速</option>
            <option value="notionInlineCode">Notion行内代码</option>
            <option value="notionAddComment">Notion添加评论</option>
            <option value="disabled">禁用</option>
          </select>
        </div>

        <div class="key-mapping-item">
          <label class="mapping-label">前进/倍速:</label>
          <input type="text" id="key-forwardOrSpeed" class="key-input" maxlength="1" placeholder="按键">
          <select id="action-forwardOrSpeed" class="action-select">
            <option value="forwardOrSpeed">前进/倍速</option>
            <option value="rewind">视频后退5秒</option>
            <option value="toggle">播放/暂停</option>
            <option value="notionInlineCode">Notion行内代码</option>
            <option value="notionAddComment">Notion添加评论</option>
            <option value="disabled">禁用</option>
          </select>
        </div>

        <div class="key-mapping-item">
          <label class="mapping-label">Notion行内代码:</label>
          <input type="text" id="key-notionInlineCode" class="key-input" maxlength="1" placeholder="按键">
          <select id="action-notionInlineCode" class="action-select">
            <option value="notionInlineCode">Notion行内代码</option>
            <option value="rewind">视频后退5秒</option>
            <option value="toggle">播放/暂停</option>
            <option value="forwardOrSpeed">前进/倍速</option>
            <option value="notionAddComment">Notion添加评论</option>
            <option value="disabled">禁用</option>
          </select>
        </div>

        <div class="key-mapping-item">
          <label class="mapping-label">Notion添加评论:</label>
          <input type="text" id="key-notionAddComment" class="key-input" maxlength="1" placeholder="按键">
          <select id="action-notionAddComment" class="action-select">
            <option value="notionAddComment">Notion添加评论</option>
            <option value="rewind">视频后退5秒</option>
            <option value="toggle">播放/暂停</option>
            <option value="forwardOrSpeed">前进/倍速</option>
            <option value="notionInlineCode">Notion行内代码</option>
            <option value="disabled">禁用</option>
          </select>
        </div>
      </div>

      <div class="panel-controls">
        <button id="saveCustomKeys" class="control-btn primary">保存设置</button>
        <button id="resetToDefault" class="control-btn secondary">恢复默认</button>
      </div>
    </div>

    <div class="footer">
      <p class="usage-tip">💡 在Notion页面使用快捷键控制同窗口内的视频，或使用快捷键 <code class="key notion-key-inline">4</code> <code class="key notion-key-inline">5</code> 进行 Notion 操作。</p>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>