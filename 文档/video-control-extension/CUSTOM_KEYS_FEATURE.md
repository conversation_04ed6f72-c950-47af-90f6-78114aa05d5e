# 🎛️ 自定义按键功能详细说明

## 功能概述

视频控制扩展 v3.1 新增了强大的自定义按键功能，允许用户根据个人习惯自由配置快捷键映射。

## 🚀 主要特性

### 1. 灵活的按键映射
- 支持为所有功能重新分配按键
- 支持禁用不需要的功能
- 实时生效，无需重启浏览器

### 2. 智能冲突检测
- 自动检测按键冲突
- 提供清晰的错误提示
- 防止无效配置保存

### 3. 直观的设置界面
- 滑动面板设计，操作流畅
- 实时预览按键配置
- 一键恢复默认设置

### 4. 数据持久化
- 配置自动保存到浏览器存储
- 跨标签页实时同步
- 支持配置导入/导出（计划功能）

## 🎮 支持的功能

| 功能 | 默认按键 | 说明 |
|------|----------|------|
| 切换扩展 | ` | 必须功能，不可禁用 |
| 视频后退 | 1 | 后退5秒 |
| 播放/暂停 | 2 | 切换播放状态 |
| 前进/倍速 | 3 | 短按前进5秒，长按3倍速 |
| Notion行内代码 | 4 | 模拟 Cmd/Ctrl+E |
| Notion添加评论 | 5 | 模拟 Shift+Cmd/Ctrl+M |

## 🔧 技术实现

### 数据结构
```javascript
// 默认按键映射
const defaultKeyMappings = {
  '`': 'toggleExtension',
  '1': 'rewind',
  '2': 'toggle',
  '3': 'forwardOrSpeed',
  '4': 'notionInlineCode',
  '5': 'notionAddComment'
};
```

### 核心组件

#### 1. 动态按键处理 (content.js)
- 重构了按键事件监听逻辑
- 支持动态按键映射查找
- 实时响应配置变更

#### 2. 设置界面 (popup.html/css/js)
- 新增自定义按键设置面板
- 响应式设计，支持移动端
- 表单验证和错误处理

#### 3. 数据同步机制
- 使用 Chrome Storage API 持久化配置
- 跨标签页实时同步按键映射
- 监听存储变化事件

## 📱 使用指南

### 打开设置界面
1. 点击浏览器工具栏中的扩展图标
2. 在弹出的控制面板中点击"自定义按键"按钮
3. 设置面板将从右侧滑入

### 修改按键映射
1. 在按键输入框中输入新的按键
2. 在下拉菜单中选择对应的功能
3. 系统会自动检测按键冲突
4. 点击"保存设置"应用更改

### 恢复默认设置
1. 在设置面板中点击"恢复默认"按钮
2. 确认操作后所有按键将重置为默认配置

## 🔍 故障排除

### 按键不生效
- 确保扩展已启用
- 检查按键映射是否正确保存
- 刷新页面重新加载扩展

### 设置界面无法打开
- 检查扩展是否正常安装
- 尝试重新加载扩展
- 查看浏览器控制台错误信息

### 按键冲突
- 系统会自动检测并提示冲突
- 为冲突的按键分配不同的功能
- 或禁用不需要的功能

## 🚧 计划功能

### v3.2 计划
- [ ] 支持组合键（Ctrl+字母等）
- [ ] 按键配置导入/导出
- [ ] 更多可绑定的功能
- [ ] 按键使用统计

### v3.3 计划
- [ ] 可视化按键编辑器
- [ ] 预设配置模板
- [ ] 云端配置同步

## 🤝 贡献指南

欢迎提交功能建议和bug报告！

### 开发环境设置
1. 克隆项目到本地
2. 在Chrome中加载解压的扩展
3. 修改代码后重新加载扩展测试

### 代码规范
- 使用ES6+语法
- 遵循现有的代码风格
- 添加必要的注释
- 确保向后兼容性

## 📄 许可证

此项目仅供个人学习和使用。
