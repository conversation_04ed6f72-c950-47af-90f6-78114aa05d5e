/* Toast通知样式 */
.video-control-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  z-index: 10000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  max-width: 300px;
  word-wrap: break-word;
}

.video-control-toast.show {
  transform: translateX(0);
  opacity: 1;
}

/* 确保Toast不会被其他元素遮挡 */
.video-control-toast {
  z-index: 2147483647 !important;
}

/* 为不同状态添加不同颜色 */
.video-control-toast.success {
  background: rgba(34, 197, 94, 0.9);
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.video-control-toast.warning {
  background: rgba(251, 146, 60, 0.9);
  border: 1px solid rgba(251, 146, 60, 0.3);
}

.video-control-toast.error {
  background: rgba(239, 68, 68, 0.9);
  border: 1px solid rgba(239, 68, 68, 0.3);
} 