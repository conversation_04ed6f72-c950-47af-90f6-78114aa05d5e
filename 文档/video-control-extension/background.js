// 后台脚本：处理标签页间的消息传递

let targetVideoTabId = null;
let lastManuallyFocusedVideoTabId = null; // 新增：存储用户最后手动聚焦的视频标签页

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'VIDEO_CONTROL') {
    // 从Notion页面发送视频控制命令
    controlVideoInSameWindow(message, sender.tab.windowId);
  } else if (message.type === 'SHOW_NOTIFICATION') {
    // 显示通知
    showNotification(message.text, sender.tab.windowId);
  }
});

// 在同一窗口内查找并控制视频
async function controlVideoInSameWindow(message, windowId) {
  try {
    // 获取同一窗口内的所有标签页
    const tabs = await chrome.tabs.query({ windowId: windowId });
    
    let videoTabsToControl = [];

    // 新的标签页选择逻辑：
    // 1. 尝试寻找当前窗口中活动的视频标签页
    const activeVideoTab = tabs.find(tab => tab.active && tab.url && isVideoUrl(tab.url));

    if (activeVideoTab) {
      videoTabsToControl.push(activeVideoTab);
    } else {
      // 2. 如果没有活动的视频标签页，则控制当前窗口中找到的第一个视频标签页
      const firstVideoTabInWindow = tabs.find(tab => tab.url && isVideoUrl(tab.url));
      if (firstVideoTabInWindow) {
        videoTabsToControl.push(firstVideoTabInWindow);
      }
    }

    // 如果没有找到任何可控制的视频标签页
    if (videoTabsToControl.length === 0) {
      console.log('在当前窗口未找到可控制的视频标签页。');
      // 可以考虑在这里给用户一个提示
      // showNotification('未找到视频播放页面', windowId);
      return;
    }
    
    // 向选定的视频标签页发送控制命令
    for (const tab of videoTabsToControl) {
      try {
        await chrome.tabs.sendMessage(tab.id, {
          type: 'EXECUTE_VIDEO_CONTROL',
          action: message.action,
          value: message.value
        });
      } catch (error) {
        console.log(`无法向标签页 ${tab.id} 发送消息:`, error);
      }
    }
  } catch (error) {
    console.error('控制视频时出错:', error);
  }
}

// 显示通知到指定窗口的活动标签页
async function showNotification(text, windowId) {
  try {
    const [activeTab] = await chrome.tabs.query({ 
      active: true, 
      windowId: windowId 
    });
    
    if (activeTab) {
      chrome.tabs.sendMessage(activeTab.id, {
        type: 'SHOW_TOAST',
        message: text
      });
    }
  } catch (error) {
    console.error('显示通知时出错:', error);
  }
}

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener(() => {
  console.log('视频控制扩展已安装');
});

// 辅助函数：判断URL是否为视频网站
function isVideoUrl(url) {
  if (!url) return false;
  return url.includes('bilibili.com') || url.includes('youtube.com') || url.includes('tiktok.com') || url.includes('douyin.com');
}

// 监听标签页激活事件
chrome.tabs.onActivated.addListener(async (activeInfo) => {
  try {
    const tab = await chrome.tabs.get(activeInfo.tabId);
    if (tab && tab.windowId === activeInfo.windowId && isVideoUrl(tab.url)) {
      targetVideoTabId = tab.id;
      // 当用户切换到视频标签页时，也将其视为一次"手动聚焦"
      lastManuallyFocusedVideoTabId = tab.id;
      console.log(`目标视频标签页已更新为: ${targetVideoTabId}`);
    } else if (tab && tab.id === targetVideoTabId) {
      // 如果当前激活的标签不是视频网站，但之前是目标，则清除
      // targetVideoTabId = null; // 暂时不清除，除非有更好的策略
    }
  } catch (error) {
    console.error('更新激活标签页时出错:', error);
  }
});

// 监听标签页更新事件 (例如URL改变)
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (tabId === targetVideoTabId && changeInfo.url && !isVideoUrl(changeInfo.url)) {
    // 如果目标视频标签页导航到了非视频页面，清除它
    console.log(`目标视频标签页 ${tabId} 不再是视频页面，已清除。`);
    targetVideoTabId = null;
    if (lastManuallyFocusedVideoTabId === tabId) {
        lastManuallyFocusedVideoTabId = null;
    }
  }
  // 如果一个非目标的标签页变成了视频页，并且用户正在看它，更新为目标
  if (changeInfo.url && isVideoUrl(changeInfo.url) && tab.active) {
    targetVideoTabId = tabId;
    lastManuallyFocusedVideoTabId = tabId; // 也更新手动聚焦
    console.log(`活动标签页 ${tabId} 更新为视频页面，设为目标。`);
  }
});

// 监听标签页移除事件
chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
  if (tabId === targetVideoTabId) {
    console.log(`目标视频标签页 ${tabId} 已关闭，已清除。`);
    targetVideoTabId = null;
  }
  if (tabId === lastManuallyFocusedVideoTabId) {
    lastManuallyFocusedVideoTabId = null;
  }
}); 