// 内容脚本：处理快捷键和视频控制

// 全局变量
let isExtensionEnabled = false;
let isKey3Pressed = false;
let originalPlaybackRate = 1;
let key3PressTimer = null;
let customKeyMappings = {}; // 存储自定义按键映射

// 默认按键映射
const defaultKeyMappings = {
  '`': 'toggleExtension',
  '1': 'rewind',
  '2': 'toggle',
  '3': 'forwardOrSpeed',
  '4': 'notionInlineCode',
  '5': 'notionAddComment'
};

// 初始化
init();

function init() {
  // 从存储中读取扩展状态和自定义按键映射
  chrome.storage.local.get(['extensionEnabled', 'customKeyMappings'], (result) => {
    isExtensionEnabled = result.extensionEnabled !== false; // 默认启用
    customKeyMappings = result.customKeyMappings || defaultKeyMappings;
  });

  // 设置键盘事件监听
  setupKeyboardListeners();

  // 监听来自background的消息
  chrome.runtime.onMessage.addListener(handleBackgroundMessage);

  // 监听存储变化，实时更新按键映射
  chrome.storage.onChanged.addListener((changes, namespace) => {
    if (namespace === 'local') {
      if (changes.extensionEnabled) {
        isExtensionEnabled = changes.extensionEnabled.newValue;
      }
      if (changes.customKeyMappings) {
        customKeyMappings = changes.customKeyMappings.newValue || defaultKeyMappings;
      }
    }
  });
}

// 设置键盘事件监听
function setupKeyboardListeners() {
  // 使用capture阶段监听，优先级更高
  document.addEventListener('keydown', handleKeyDown, true);
  document.addEventListener('keyup', handleKeyUp, true);

  // 为了更彻底地防止字符输入，也监听keypress事件
  document.addEventListener('keypress', handleKeyPress, true);
}

// 处理keypress事件，进一步阻止字符输入
function handleKeyPress(event) {
  const key = event.key;

  // 检查是否是我们要拦截的按键（动态获取）
  const targetKeys = Object.keys(customKeyMappings);
  if (!targetKeys.includes(key)) {
    return;
  }

  // 如果扩展启用且应该处理按键事件，就阻止字符输入
  const action = customKeyMappings[key];
  if (shouldHandleKeyEvent(event) && (action === 'toggleExtension' || isExtensionEnabled)) {
    event.preventDefault();
    event.stopPropagation();
    event.stopImmediatePropagation();
  }
}

// 处理按键按下
function handleKeyDown(event) {
  const key = event.key;

  // 先检查是否是我们要处理的按键（动态获取）
  const targetKeys = Object.keys(customKeyMappings);
  if (!targetKeys.includes(key)) {
    return;
  }

  // 检查是否应该处理这个按键事件
  if (!shouldHandleKeyEvent(event)) {
    return;
  }

  const action = customKeyMappings[key];

  // 处理切换扩展状态
  if (action === 'toggleExtension') {
    event.preventDefault();
    event.stopPropagation();
    event.stopImmediatePropagation();
    toggleExtension();
    return;
  }

  // 如果扩展未启用，不处理其他按键
  if (!isExtensionEnabled) {
    return;
  }

  // 对于视频控制按键和 Notion 特定按键，都要阻止默认行为
  // 但仅在非 Notion 页面，或 Notion 页面但按键是视频控制时阻止
  const videoControlActions = ['rewind', 'toggle', 'forwardOrSpeed'];
  if (!window.location.hostname.includes('notion') || videoControlActions.includes(action)) {
    event.preventDefault();
    event.stopPropagation();
    event.stopImmediatePropagation();
  }

  // 处理各种动作
  switch (action) {
    case 'rewind':
      sendVideoControl('rewind', 5);
      break;

    case 'toggle':
      sendVideoControl('toggle');
      break;

    case 'forwardOrSpeed':
      if (!isKey3Pressed) {
        isKey3Pressed = true;

        // 设置长按检测定时器（300ms后触发倍速播放）
        key3PressTimer = setTimeout(() => {
          // 长按：发送带有特定标记的speed命令
          sendVideoControl('speed', { target: 3, isKey3Start: true });
          key3PressTimer = null; // 标记为长按状态
        }, 300);
      }
      break;

    case 'notionInlineCode':
      if (window.location.hostname.includes('notion')) {
        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();
        simulateNotionShortcut('inlineCode');
      }
      break;

    case 'notionAddComment':
      if (window.location.hostname.includes('notion')) {
        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();
        simulateNotionShortcut('addComment');
      }
      break;
  }
}

// 处理按键松开
function handleKeyUp(event) {
  const key = event.key;

  // 只处理我们关注的按键（动态获取）
  const targetKeys = Object.keys(customKeyMappings);
  if (!targetKeys.includes(key)) {
    return;
  }

  if (!shouldHandleKeyEvent(event)) {
    return;
  }

  // 对于我们的按键，都要阻止默认行为
  event.preventDefault();
  event.stopPropagation();
  event.stopImmediatePropagation();

  const action = customKeyMappings[key];

  // 处理forwardOrSpeed键松开
  if (action === 'forwardOrSpeed' && isKey3Pressed) {
    isKey3Pressed = false;

    if (key3PressTimer === null) {
      // key3PressTimer为null表示已经触发了长按（倍速播放），现在恢复原速
      sendVideoControl('speed', 'restore');
    } else {
      // key3PressTimer还存在表示是短按，清除定时器并执行前进5秒
      clearTimeout(key3PressTimer);
      key3PressTimer = null;
      sendVideoControl('forward', 5);
    }
  }
}

// 判断是否应该处理键盘事件
function shouldHandleKeyEvent(event) {
  // 如果按下了修饰键 (除了 Notion 添加评论时的 Shift)，不处理
  // 对于 '5' (添加评论)，允许 Shift 键
  if (event.key !== '5' && (event.ctrlKey || event.altKey || event.metaKey || event.shiftKey)) {
    return false;
  }
  // 如果是 '5'，只允许 Shift + Meta/Ctrl
  if (event.key === '5' && !( (event.metaKey || event.ctrlKey) && event.shiftKey && !event.altKey)) {
    // 如果不是 Notion 页面，或者按键不是 5，或者修饰键不满足，则正常判断
    if(!window.location.hostname.includes('notion') || event.key !== '5'){
        if (event.ctrlKey || event.altKey || event.metaKey || event.shiftKey) {
            return false;
        }
    } else if (event.key === '5' && window.location.hostname.includes('notion')) {
        // 如果是 Notion 且是按键 5, 但修饰键不是 Shift+Cmd/Ctrl，则不处理
        // 这是为了让原生的 Shift+5 (例如打出 %) 能够工作
        if (!((event.metaKey || event.ctrlKey) && event.shiftKey)) {
           // return false; // 暂时注释掉，因为我们的逻辑是单独按5
        }
    }
  }

  const activeElement = document.activeElement;
  const tagName = activeElement.tagName.toLowerCase();

  // 在普通输入框、文本域等表单元素中不处理
  if (['input', 'textarea', 'select'].includes(tagName)) {
    return false;
  }

  // 如果是在Notion页面，我们要特殊处理
  if (window.location.hostname.includes('notion')) {
    // 在Notion中，即使是contentEditable元素，我们也要拦截按键
    // 但要更加谨慎，确保真的要拦截
    return true;
  }

  // 在其他网站的可编辑元素中不处理
  if (activeElement.isContentEditable) {
    return false;
  }

  return true;
}

// 切换扩展启用状态
function toggleExtension() {
  isExtensionEnabled = !isExtensionEnabled;

  // 保存状态到存储
  chrome.storage.local.set({ extensionEnabled: isExtensionEnabled });

  // 显示状态提示
  const message = isExtensionEnabled ? '视频控制扩展已启用' : '视频控制扩展已禁用';
  chrome.runtime.sendMessage({
    type: 'SHOW_NOTIFICATION',
    text: message,
    noToast: true // 为切换功能时不显示toast，除非特别指定
  });
}

// 发送视频控制命令
function sendVideoControl(action, value = null) {
  // 如果当前页面就是视频页面，直接控制
  if (isVideoPage()) {
    executeVideoControl(action, value);
  } else {
    // 否则通过background script转发到其他标签页
    chrome.runtime.sendMessage({
      type: 'VIDEO_CONTROL',
      action: action,
      value: value
    });
  }
}

// 判断当前页面是否是视频页面
function isVideoPage() {
  const hostname = window.location.hostname;
  return hostname.includes('bilibili.com') || hostname.includes('youtube.com') || hostname.includes('tiktok.com') || hostname.includes('douyin.com');
}

// 处理来自background的消息
function handleBackgroundMessage(message, sender, sendResponse) {
  if (message.type === 'EXECUTE_VIDEO_CONTROL') {
    executeVideoControl(message.action, message.value);
  } else if (message.type === 'SHOW_TOAST') {
    showToast(message.message);
  } else if (message.type === 'EXTENSION_STATUS_CHANGED') {
    isExtensionEnabled = message.enabled;
    // 仅在非静默模式下显示 Toast
    if (!message.silent) {
        showToast(message.message);
    }
  } else if (message.type === 'KEY_MAPPING_CHANGED') {
    customKeyMappings = message.mappings;
    showToast('按键映射已更新');
  }
}

// 执行视频控制
function executeVideoControl(action, value) {
  const video = findVideoElement();
  if (!video) return;

  switch (action) {
    case 'toggle':
      if (video.paused) {
        video.play();
      } else {
        video.pause();
      }
      break;

    case 'rewind':
      video.currentTime = Math.max(0, video.currentTime - (value || 5));
      break;

    case 'forward':
      video.currentTime = Math.min(video.duration, video.currentTime + (value || 5));
      break;

    case 'speed':
      if (value === 'restore') {
        // 当松开按键 "3" 时恢复原始播放速率
        video.playbackRate = originalPlaybackRate;
      } else if (typeof value === 'object' && value !== null && value.target !== undefined) {
        // 当长按按键 "3" 启动时
        const targetSpeed = value.target;
        const isKey3StartCmd = value.isKey3Start || false;

        if (isKey3StartCmd) {
          // 保存当前的播放速率，以便后续恢复
          originalPlaybackRate = video.playbackRate;
        }

        // 如果视频已暂停，则开始播放
        if (video.paused) {
          video.play();
        }
        // 设置为目标播放速率 (例如 3倍速)
        video.playbackRate = targetSpeed;
      } else if (typeof value === 'number') {
        // 处理其他直接设置速度的命令 (不影响 originalPlaybackRate)
        video.playbackRate = value;
      }
      break;
  }
}

// 查找视频元素
function findVideoElement() {
  const hostname = window.location.hostname;
  let video;

  // 针对特定网站的精准选择器
  if (hostname.includes('tiktok.com')) {
    // TikTok 的选择器
    video = document.querySelector('div[data-e2e="video-player-container"] video');
    if (video) return video;
  }

  if (hostname.includes('douyin.com')) {
    // 抖音的选择器 (尝试了两种常见的)
    video = document.querySelector('.player video, .xg-video-container video, #LVideoPlayer video');
    if (video) return video;
  }

  // B站和油管的视频元素通常就是<video>，但我们也可以让它更精确
  if (hostname.includes('bilibili.com')) {
    video = document.querySelector('.bpx-player-video-wrap video, .bilibili-player-video video');
    if(video) return video;
  }

  if (hostname.includes('youtube.com')) {
    video = document.querySelector('.html5-main-video');
    if(video) return video;
  }


  // 如果特定选择器没有找到，使用通用的后备方案
  const videos = Array.from(document.querySelectorAll('video'));

  // 优先返回可见、有内容且正在播放或暂停的视频
  const visibleVideos = videos.filter(v => v.duration > 0 && !v.hidden && v.offsetParent !== null && v.readyState > 0);
  
  if (visibleVideos.length > 0) {
    // 如果有多个可见视频，尝试找到尺寸最大的那个，通常是主视频
    if (visibleVideos.length > 1) {
      return visibleVideos.sort((a, b) => (b.clientWidth * b.clientHeight) - (a.clientWidth * a.clientHeight))[0];
    }
    return visibleVideos[0];
  }

  // 如果没有找到播放中的，返回第一个可见的视频元素
  const anyVisibleVideo = videos.find(v => v.offsetParent !== null);
  if (anyVisibleVideo) {
    return anyVisibleVideo;
  }

  // 最后，作为最终的后备，返回页面上的第一个video元素
  return videos.length > 0 ? videos[0] : null;
}

// 显示Toast通知
function showToast(message) {
  // 检查是否在Notion页面，并且是与按键4或5相关的消息
  // 如果是，则不显示Toast (这些操作不需要提示)
  if (window.location.hostname.includes('notion') &&
      (message.includes('行内代码') || message.includes('评论'))) {
    return;
  }

  // 移除已存在的toast
  const existingToast = document.getElementById('video-control-toast');
  if (existingToast) {
    existingToast.remove();
  }

  // 创建toast元素
  const toast = document.createElement('div');
  toast.id = 'video-control-toast';
  toast.className = 'video-control-toast';
  toast.textContent = message;

  // 添加到页面
  document.body.appendChild(toast);

  // 动画显示
  setTimeout(() => {
    toast.classList.add('show');
  }, 10);

  // 3秒后隐藏
  setTimeout(() => {
    toast.classList.remove('show');
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 300);
  }, 3000);
}

// 新增：模拟Notion快捷键
function simulateNotionShortcut(action) {
  const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
  const metaOrCtrlKey = isMac ? 'metaKey' : 'ctrlKey';

  let eventProps;

  if (action === 'inlineCode') {
    eventProps = {
      key: 'e',
      code: 'KeyE',
      keyCode: 69,
      which: 69,
      bubbles: true,
      cancelable: true,
      composed: true
    };
    eventProps[metaOrCtrlKey] = true;
  } else if (action === 'addComment') {
    eventProps = {
      key: 'm',
      code: 'KeyM',
      keyCode: 77,
      which: 77,
      bubbles: true,
      cancelable: true,
      composed: true,
      shiftKey: true
    };
    eventProps[metaOrCtrlKey] = true;
  } else {
    return;
  }

  // Notion 的编辑器通常是 document.activeElement 或者其父级
  let targetElement = document.activeElement;

  // 尝试在当前焦点元素上触发
  if (targetElement && typeof targetElement.dispatchEvent === 'function') {
    targetElement.dispatchEvent(new KeyboardEvent('keydown', eventProps));
    targetElement.dispatchEvent(new KeyboardEvent('keyup', eventProps));
  } else {
    // 如果焦点元素不合适，尝试在body上触发 (Notion某些情况可能需要)
    document.body.dispatchEvent(new KeyboardEvent('keydown', eventProps));
    document.body.dispatchEvent(new KeyboardEvent('keyup', eventProps));
  }
}