* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Aria<PERSON>, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
  line-height: 1.6;
  min-height: 100vh;
}

.popup-container {
  width: 360px;
  min-height: 480px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin: 0;
}

.status-section {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-label {
  font-size: 14px;
  color: #666;
}

.status-value {
  font-size: 14px;
  font-weight: 500;
  color: #10b981;
  padding: 4px 8px;
  background: #ecfdf5;
  border-radius: 4px;
}

.status-value.disabled {
  color: #ef4444;
  background: #fef2f2;
}

.shortcuts-section, .supported-sites {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}

.shortcuts-list, .sites-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.shortcut-item, .site-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.key {
  font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
  font-size: 13px;
  font-weight: bold;
  color: #667eea;
  background: #f8fafc;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  min-width: 60px;
  text-align: center;
}

.key.notion-key {
  color: #ff69b4; /* Notion 风格的粉色 */
  border-color: #ffc0cb;
}

.key.notion-key-inline {
  font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
  font-size: 11px; /* 比普通key小一点 */
  font-weight: bold;
  color: #ff69b4;
  background: #fff0f5; /* 淡粉色背景 */
  padding: 2px 5px;
  border-radius: 3px;
  border: 1px solid #ffc0cb;
  margin: 0 2px;
}

.description {
  font-size: 13px;
  color: #666;
  flex: 1;
  margin-left: 12px;
}

.site-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.site-url {
  font-size: 12px;
  color: #666;
  font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
}

.sites-list {
  display: flex;
  flex-wrap: wrap; /* 允许换行 */
  gap: 8px;
}

.site-badge {
  font-size: 13px;
  color: #555;
  background: #f0f0f0;
  padding: 6px 10px;
  border-radius: 16px; /* 圆角徽章 */
  border: 1px solid #e0e0e0;
}

.controls {
  padding: 20px;
  text-align: center;
  display: flex;
  gap: 10px;
  justify-content: center;
}

.control-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.control-btn.secondary {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

.control-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.control-btn:active {
  transform: translateY(0);
}

.control-btn.disabled {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.footer {
  padding: 16px 20px;
  background: #f8fafc;
  text-align: center;
  border-top: 1px solid #f0f0f0; /* 添加上边框 */
}

.usage-tip {
  font-size: 12px;
  color: #666;
  margin: 0;
}

/* 自定义按键面板样式 */
.customize-panel {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: white;
  z-index: 1000;
  overflow-y: auto;
  transition: all 0.3s ease;
}

.customize-panel.hidden {
  transform: translateX(100%);
  opacity: 0;
  pointer-events: none;
}

.panel-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.key-mappings {
  padding: 20px;
}

.key-mapping-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #f8fafc;
}

.mapping-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  min-width: 100px;
}

.key-input {
  width: 60px;
  padding: 8px 12px;
  border: 2px solid #d1d5db;
  border-radius: 6px;
  font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  background: white;
  transition: border-color 0.2s ease;
}

.key-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.key-input.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.action-select {
  flex: 1;
  padding: 8px 12px;
  border: 2px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s ease;
}

.action-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.action-select:disabled {
  background: #f3f4f6;
  color: #6b7280;
  cursor: not-allowed;
}

.panel-controls {
  padding: 20px;
  border-top: 1px solid #e2e8f0;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.error-message {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
  display: none;
}

.error-message.show {
  display: block;
}

/* 响应式设计 */
@media (max-width: 400px) {
  .popup-container {
    width: 320px;
  }

  .shortcut-item, .site-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .description {
    margin-left: 0;
  }

  .key-mapping-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .mapping-label {
    min-width: auto;
  }

  .panel-controls {
    flex-direction: column;
  }
}