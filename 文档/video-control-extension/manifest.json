{"manifest_version": 3, "name": "视频控制扩展", "version": "3.1", "description": "在Notion页面控制同窗口内哔哩哔哩和YouTube视频的播放", "permissions": ["activeTab", "tabs", "storage"], "host_permissions": ["https://*.bilibili.com/*", "https://*.youtube.com/*", "https://*.notion.so/*", "https://notion.so/*", "https://*.tiktok.com/*", "https://*.douyin.com/*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["https://*.bilibili.com/*", "https://*.youtube.com/*", "https://*.notion.so/*", "https://notion.so/*", "https://*.tiktok.com/*", "https://*.douyin.com/*"], "js": ["content.js"], "css": ["styles.css"], "run_at": "document_end"}], "action": {"default_popup": "popup.html", "default_title": "视频控制扩展", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}