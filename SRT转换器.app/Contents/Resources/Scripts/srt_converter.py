#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SRT字幕转换器
功能：将剪贴板中的文本转换为SRT格式的字幕文件
作者：Augment AI
日期：2025-05-09
"""

import re
import os
import sys
import unicodedata
from datetime import datetime
import subprocess
import platform

# 全局配置
CHAR_DURATION_MS = 250  # 每字符显示时间（毫秒）
# 将日志文件保存到应用程序内部，而不是桌面
APP_PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
LOG_FILE = os.path.join(APP_PATH, "Contents", "Resources", "srt_converter.log")

def setup_logging():
    """设置简单的日志记录"""
    import logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            # 只使用控制台日志，不再创建日志文件
            logging.StreamHandler()
        ]
    )
    return logging.getLogger("SRT转换器")

# 初始化日志
logger = setup_logging()

def normalize_text(text):
    """规范化文本，移除不可见字符和控制字符，但保留基本空白"""
    result = []
    for char in text:
        # 保留基本空白字符和可见字符
        if char in ' \t\n' or not unicodedata.category(char).startswith(('C', 'Z')):
            result.append(char)
    return ''.join(result)

def clean_xml_tags(text):
    """清理无用的XML/SSML标签，但保留 <break> 标签"""
    # 先保存所有的 <break> 标签，用占位符替换
    break_pattern = r'<break\s+time\s*=\s*["\'](\d+(?:\.\d+)?)ms["\']\s*/>'
    break_tags = []
    break_placeholder = "___BREAK_PLACEHOLDER_{}_____"

    # 找到所有 break 标签并用占位符替换
    def save_break_tag(match):
        break_tags.append(match.group(0))
        return break_placeholder.format(len(break_tags) - 1)

    text_with_placeholders = re.sub(break_pattern, save_break_tag, text, flags=re.IGNORECASE)

    # 清理所有其他XML标签（包括开始标签和结束标签）
    # 匹配 <标签名 属性...> 或 </标签名> 格式
    xml_pattern = r'</?[a-zA-Z][a-zA-Z0-9:_-]*(?:\s+[^>]*)?>'
    cleaned_text = re.sub(xml_pattern, '', text_with_placeholders)

    # 恢复 break 标签
    for i, break_tag in enumerate(break_tags):
        placeholder = break_placeholder.format(i)
        cleaned_text = cleaned_text.replace(placeholder, break_tag)

    # 清理多余的空白字符
    cleaned_text = re.sub(r'\n\s*\n', '\n', cleaned_text)  # 移除多余的空行
    cleaned_text = re.sub(r'[ \t]+', ' ', cleaned_text)     # 合并多个空格为一个

    return cleaned_text.strip()

def parse_time_to_ms(time_str):
    """将 HH:MM:SS,ms 格式的时间字符串转换为总毫秒数"""
    match = re.match(r"(\d{2}):(\d{2}):(\d{2}),(\d{3})", time_str)
    if not match:
        raise ValueError(f"无效的时间字符串格式: {time_str}")
    h, m, s, ms_val = map(int, match.groups())
    return (h * 3600 + m * 60 + s) * 1000 + ms_val

def format_ms_to_time(total_ms):
    """将总毫秒数转换为 HH:MM:SS,ms 格式的时间字符串"""
    if total_ms < 0:
        raise ValueError("总毫秒数不能为负")

    ms_val = total_ms % 1000
    total_seconds = total_ms // 1000
    s_val = total_seconds % 60
    total_minutes = total_seconds // 60
    m_val = total_minutes % 60
    h_val = total_minutes // 60

    return f"{h_val:02d}:{m_val:02d}:{s_val:02d},{ms_val:03d}"

def is_pause_marker(line):
    """增强的暂停标记检测，支持多种格式和可能包含不可见字符的情况，支持小数秒数"""
    # 清理行，移除不可见字符
    cleaned_line = normalize_text(line)

    # 尝试多种暂停标记格式，支持小数秒数
    patterns = [
        r"^\s*\[\s*(\d+(?:\.\d+)?)\s*s\s*\]\s*$",  # [3s], [3.5s], [ 3.2 s ], 等
        r"^\s*（\s*(\d+(?:\.\d+)?)\s*秒\s*）\s*$",   # （3秒）, （3.5秒）
        r"^\s*\(\s*(\d+(?:\.\d+)?)\s*s\s*\)\s*$",   # (3s), (3.5s)
        r"^\s*\(\s*(\d+(?:\.\d+)?)\s*秒\s*\)\s*$",   # (3秒), (3.5秒)
        r"^\s*暂停\s*(\d+(?:\.\d+)?)\s*秒\s*$",      # 暂停3秒, 暂停3.5秒
        r"^\s*停顿\s*(\d+(?:\.\d+)?)\s*秒\s*$",      # 停顿3秒, 停顿3.5秒
        r"^\s*pause\s*(\d+(?:\.\d+)?)s\s*$",        # pause 3s, pause 3.5s
    ]

    for pattern in patterns:
        match = re.match(pattern, cleaned_line, re.IGNORECASE)
        if match:
            return float(match.group(1))

    return None

def parse_inline_text(text):
    """解析包含内联暂停标记的文本，返回文本片段和暂停时间的列表，支持小数秒数和毫秒格式"""
    # 支持的内联暂停标记模式，支持小数秒数
    pause_patterns = [
        r'\[\s*(\d+(?:\.\d+)?)\s*s\s*\]',      # [3s], [3.5s], [ 3.2 s ]
        r'（\s*(\d+(?:\.\d+)?)\s*秒\s*）',       # （3秒）, （3.5秒）
        r'\(\s*(\d+(?:\.\d+)?)\s*s\s*\)',      # (3s), (3.5s)
        r'\(\s*(\d+(?:\.\d+)?)\s*秒\s*\)',      # (3秒), (3.5秒)
    ]

    # 首先处理 <break time="XXXms"/> 格式
    break_pattern = r'<break\s+time\s*=\s*["\'](\d+(?:\.\d+)?)ms["\']\s*/>'

    result = []
    current_text = text

    # 先处理所有的 <break> 标记
    break_matches = list(re.finditer(break_pattern, current_text, re.IGNORECASE))

    if break_matches:
        last_end = 0
        for match in break_matches:
            # 添加 break 标记前的文本
            if match.start() > last_end:
                text_before = current_text[last_end:match.start()].strip()
                if text_before:
                    # 递归处理这段文本中可能包含的其他暂停标记
                    sub_segments = parse_other_pause_formats(text_before, pause_patterns)
                    result.extend(sub_segments)

            # 提取毫秒数并转换为秒数
            ms_value = float(match.group(1))
            pause_seconds = ms_value / 1000.0
            result.append({'type': 'pause', 'duration': pause_seconds})

            last_end = match.end()

        # 处理最后剩余的文本
        if last_end < len(current_text):
            remaining_text = current_text[last_end:].strip()
            if remaining_text:
                sub_segments = parse_other_pause_formats(remaining_text, pause_patterns)
                result.extend(sub_segments)
    else:
        # 如果没有 <break> 标记，使用原来的逻辑处理其他格式
        result = parse_other_pause_formats(current_text, pause_patterns)

    return result

def parse_other_pause_formats(text, pause_patterns):
    """解析其他格式的暂停标记（非 <break> 格式）"""
    # 合并所有模式
    combined_pattern = '|'.join(f'({pattern})' for pattern in pause_patterns)

    result = []
    last_end = 0

    # 查找所有暂停标记
    for match in re.finditer(combined_pattern, text):
        # 添加暂停标记前的文本
        if match.start() > last_end:
            text_before = text[last_end:match.start()].strip()
            if text_before:
                result.append({'type': 'text', 'content': text_before})

        # 提取暂停时间（从匹配的组中找到非空的数字）
        pause_seconds = None
        for group in match.groups():
            if group and re.match(r'\d+(?:\.\d+)?$', group):
                pause_seconds = float(group)
                break

        if pause_seconds is not None:
            result.append({'type': 'pause', 'duration': pause_seconds})

        last_end = match.end()

    # 添加最后剩余的文本
    if last_end < len(text):
        remaining_text = text[last_end:].strip()
        if remaining_text:
            result.append({'type': 'text', 'content': remaining_text})

    return result

def generate_srt(text_input):
    """根据给定的文本输入生成 SRT 格式的字符串"""
    # 预处理输入文本
    text_input = clean_xml_tags(text_input)  # 先清理XML标签
    text_input = normalize_text(text_input)  # 再规范化文本

    srt_entries = []
    current_time_ms = 0  # 初始化为 00:00:00,000
    sequence_number = 1

    lines = text_input.strip().split('\n')
    processed_data = []  # 用于后续验证的中间数据

    logger.info(f"处理文本，共 {len(lines)} 行")

    for line_index, line_content in enumerate(lines):
        line = line_content.strip()
        if not line:  # 跳过空行
            continue

        # 首先检查是否是独立的暂停标记行（保持向后兼容）
        pause_seconds = is_pause_marker(line)

        if pause_seconds is not None:
            if pause_seconds < 0:
                raise ValueError(f"无效的停顿时间: {pause_seconds}s。必须为非负数。")

            logger.info(f"行 {line_index+1}: 识别到独立暂停标记 {pause_seconds}s")
            effective_pause_ms = int(pause_seconds * 1000)
            current_time_ms += effective_pause_ms
            processed_data.append({'type': 'pause', 'duration_ms': effective_pause_ms})
        else:
            # 解析包含内联暂停标记的文本
            parsed_segments = parse_inline_text(line)

            if not parsed_segments:
                continue

            logger.info(f"行 {line_index+1}: 解析出 {len(parsed_segments)} 个片段")

            for segment in parsed_segments:
                if segment['type'] == 'pause':
                    pause_duration = segment['duration']
                    if pause_duration < 0:
                        raise ValueError(f"无效的停顿时间: {pause_duration}s。必须为非负数。")

                    logger.info(f"  - 暂停 {pause_duration}s")
                    effective_pause_ms = int(pause_duration * 1000)
                    current_time_ms += effective_pause_ms
                    processed_data.append({'type': 'pause', 'duration_ms': effective_pause_ms})

                elif segment['type'] == 'text':
                    text_content = segment['content']
                    if not text_content:
                        continue

                    logger.info(f"  - 文本: '{text_content[:20]}...'")
                    text_duration_ms = len(text_content) * CHAR_DURATION_MS

                    start_time_ms = current_time_ms
                    end_time_ms = start_time_ms + text_duration_ms

                    start_time_str = format_ms_to_time(start_time_ms)
                    end_time_str = format_ms_to_time(end_time_ms)

                    entry = {
                        "seq": sequence_number,
                        "start_ms": start_time_ms,
                        "end_ms": end_time_ms,
                        "start_str": start_time_str,
                        "end_str": end_time_str,
                        "text": text_content
                    }
                    srt_entries.append(entry)
                    processed_data.append({'type': 'text', 'entry': entry})

                    current_time_ms = end_time_ms
                    sequence_number += 1

    # --- 验证生成的字幕 ---
    logger.info("验证生成的字幕")
    validate_srt_entries(processed_data)

    # --- 格式化 SRT 输出 ---
    logger.info(f"验证通过，生成 SRT 文件，共 {len(srt_entries)} 条字幕")
    srt_output_lines = []
    for entry in srt_entries:
        srt_output_lines.append(str(entry["seq"]))
        srt_output_lines.append(f"{entry['start_str']} --> {entry['end_str']}")
        srt_output_lines.append(entry["text"])
        srt_output_lines.append("")

    return "\n".join(srt_output_lines).strip()

def validate_srt_entries(processed_data):
    """验证生成的SRT条目是否有效"""
    last_effective_end_time_ms = 0
    expected_seq = 1

    for item_data in processed_data:
        if item_data['type'] == 'text':
            entry = item_data['entry']

            if entry["seq"] != expected_seq:
                raise ValueError(f"序号验证失败。预期: {expected_seq}, 实际: {entry['seq']}")
            expected_seq += 1

            try:
                if parse_time_to_ms(entry["start_str"]) != entry["start_ms"]:
                    raise ValueError(f"内部时间转换错误 (开始时间) 条目 {entry['seq']}.")
                if parse_time_to_ms(entry["end_str"]) != entry["end_ms"]:
                    raise ValueError(f"内部时间转换错误 (结束时间) 条目 {entry['seq']}.")
            except ValueError as e:
                 raise ValueError(f"时间码格式验证失败 条目 {entry['seq']}: {e}")

            if entry["start_ms"] >= entry["end_ms"]:
                 raise ValueError(f"时间码验证失败 条目 {entry['seq']}: 开始时间必须小于结束时间")

            if entry["start_ms"] != last_effective_end_time_ms:
                raise ValueError(f"时间连续性中断于条目 {entry['seq']}. 预期开始时间: {format_ms_to_time(last_effective_end_time_ms)}")

            last_effective_end_time_ms = entry["end_ms"]

        elif item_data['type'] == 'pause':
            last_effective_end_time_ms += item_data['duration_ms']

def get_clipboard_content():
    """获取剪贴板内容"""
    if platform.system() == 'Darwin':  # macOS
        try:
            process = subprocess.Popen(['pbpaste'], stdout=subprocess.PIPE)
            clipboard_content, _ = process.communicate()
            return clipboard_content.decode('utf-8')
        except Exception as e:
            logger.error(f"无法读取剪贴板内容: {e}")
            show_notification("错误", f"无法读取剪贴板内容: {e}")
            sys.exit(1)
    else:
        logger.error("此脚本目前仅支持macOS系统")
        show_notification("错误", "此脚本目前仅支持macOS系统")
        sys.exit(1)

def show_notification(title, message):
    """显示系统通知"""
    if platform.system() == 'Darwin':  # macOS
        try:
            subprocess.run([
                'osascript',
                '-e',
                f'display notification "{message}" with title "{title}"'
            ])
        except Exception as e:
            logger.error(f"无法显示通知: {e}")
            # 如果通知失败，我们不做任何处理，因为这是非关键功能
            pass

def main():
    """主函数"""
    logger.info("SRT转换器启动")

    # 获取剪贴板内容
    script_input_text = get_clipboard_content()

    if not script_input_text.strip():
        logger.warning("剪贴板内容为空")
        show_notification("SRT生成失败", "剪贴板内容为空")
        sys.exit(0)

    logger.info(f"获取到剪贴板内容，长度: {len(script_input_text)} 字符")

    try:
        srt_result = generate_srt(script_input_text)

        # 确定输出文件名和路径
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"generated_srt_{timestamp}.srt"
        output_filepath = os.path.join(desktop_path, output_filename)

        with open(output_filepath, 'w', encoding='utf-8') as f:
            f.write(srt_result)

        logger.info(f"SRT文件生成成功，保存到: {output_filepath}")
        show_notification("SRT生成成功", f"已保存到: {output_filename}")

    except ValueError as e:
        logger.error(f"SRT生成失败 - 值错误: {str(e)}")
        show_notification("SRT生成失败", f"错误: {str(e)[:50]}...")
    except Exception as e:
        logger.error(f"SRT生成失败 - 意外错误: {str(e)}", exc_info=True)
        show_notification("SRT生成失败", f"意外错误: {str(e)[:50]}...")

if __name__ == "__main__":
    main()
