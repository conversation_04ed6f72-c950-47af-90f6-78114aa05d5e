<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Scripts/main.scpt</key>
		<data>
		vL6GtvDwOfbibjUnLtD5tKjfY4w=
		</data>
		<key>Resources/applet.icns</key>
		<data>
		sINd6lbiqHD5dL8c6u79cFvVXhw=
		</data>
		<key>Resources/applet.rsrc</key>
		<data>
		ar5xmDs9mPpaINVXiVlVqUCB8ao=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Resources/Scripts/main.scpt</key>
		<dict>
			<key>hash</key>
			<data>
			vL6GtvDwOfbibjUnLtD5tKjfY4w=
			</data>
			<key>hash2</key>
			<data>
			ECGJyfwkDFueGUyLPfJSOIrJiuc+AoW/O/mAKVN/QxM=
			</data>
		</dict>
		<key>Resources/applet.icns</key>
		<dict>
			<key>hash</key>
			<data>
			sINd6lbiqHD5dL8c6u79cFvVXhw=
			</data>
			<key>hash2</key>
			<data>
			J7weZ6vlnv9r32tS5HFcyuPXl2StdDnfepLxAixlryk=
			</data>
		</dict>
		<key>Resources/applet.rsrc</key>
		<dict>
			<key>hash</key>
			<data>
			ar5xmDs9mPpaINVXiVlVqUCB8ao=
			</data>
			<key>hash2</key>
			<data>
			NV2dNLwgykV7aWfTBywUILBA9Lidl7F51PrkQw77SbM=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
