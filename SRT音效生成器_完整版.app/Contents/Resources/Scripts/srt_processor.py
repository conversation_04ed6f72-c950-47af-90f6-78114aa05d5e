#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SRT音效处理器
从SRT字幕文件中提取标记词语的时间戳并生成音效轨道

用法:
    python srt_processor.py input.srt --audio audio_file output.mp3
"""

import sys
import re
import os
import subprocess
from typing import List, Dict, Tuple


def srt_time_to_ms(time_str: str) -> int:
    """将SRT时间字符串转换为毫秒"""
    try:
        parts = re.split(r'[:,]', time_str.strip())
        if len(parts) != 4:
            return 0

        hours = int(parts[0]) * 3600000
        minutes = int(parts[1]) * 60000
        seconds = int(parts[2]) * 1000
        milliseconds = int(parts[3])

        return hours + minutes + seconds + milliseconds
    except (ValueError, IndexError):
        return 0


def ms_to_seconds(ms: int) -> float:
    """将毫秒转换为秒"""
    return round(ms / 1000, 3)


def parse_srt_content(srt_content: str) -> List[Dict]:
    """解析SRT内容"""
    lines = srt_content.strip().split('\n')
    blocks = []
    current_block = None

    for line in lines:
        line = line.strip()

        if re.match(r'^\d+$', line) and current_block is None:
            current_block = {'id': line, 'time': '', 'text': []}
        elif '-->' in line and current_block:
            current_block['time'] = line
        elif line and current_block:
            current_block['text'].append(line)
        elif not line and current_block:
            blocks.append(current_block)
            current_block = None

    if current_block:
        blocks.append(current_block)

    return blocks


def extract_marked_words(text: str) -> List[Tuple[str, int]]:
    """提取被反引号标记的词语"""
    marked_words = []
    pattern = r'`([^`]+)`'

    for match in re.finditer(pattern, text):
        word = match.group(1)
        # 计算关键词的中位时间点
        text_before_marker = text[:match.start()]
        plain_text_before = re.sub(r'`', '', text_before_marker)

        # 关键词长度
        word_length = len(word)
        # 关键词中位位置（向上取整，确保在关键词内）
        word_middle_offset = (word_length + 1) // 2

        # 最终位置：关键词开始位置 + 中位偏移
        position = len(plain_text_before) + word_middle_offset
        marked_words.append((word, position))

    return marked_words


def calculate_timestamps(srt_content: str) -> List[float]:
    """计算时间戳"""
    results = []
    blocks = parse_srt_content(srt_content)

    for block in blocks:
        time_parts = block['time'].split(' --> ')
        if len(time_parts) != 2:
            continue

        start_time_ms = srt_time_to_ms(time_parts[0])
        end_time_ms = srt_time_to_ms(time_parts[1])
        duration_ms = end_time_ms - start_time_ms

        block_text_with_markers = '\n'.join(block['text'])
        plain_block_text = re.sub(r'`', '', block_text_with_markers)
        char_count = len(plain_block_text)

        time_per_char = duration_ms / char_count if char_count > 0 else 0

        marked_words = extract_marked_words(block_text_with_markers)

        for word, position in marked_words:
            offset_ms = position * time_per_char
            final_timestamp_ms = start_time_ms + offset_ms
            results.append(ms_to_seconds(int(final_timestamp_ms)))

    return results


def generate_audio_track(timestamps: List[float], audio_file: str, output_file: str) -> bool:
    """使用FFmpeg生成音效轨道"""
    if not timestamps:
        print("❌ 没有时间戳数据")
        return False

    if not os.path.exists(audio_file):
        print(f"❌ 音频文件不存在: {audio_file}")
        return False

    try:
        # 检查FFmpeg是否可用
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ FFmpeg未安装或不在PATH中")
        return False

    try:
        # 计算总时长
        duration = max(timestamps) + 5

        # 构建FFmpeg命令
        cmd = ['ffmpeg', '-y']  # -y 覆盖输出文件

        # 添加输入音频文件（为每个时间戳添加一次）
        for _ in timestamps:
            cmd.extend(['-i', audio_file])

        # 创建静音轨道作为基础
        cmd.extend(['-f', 'lavfi', '-i', f'anullsrc=channel_layout=stereo:sample_rate=44100'])

        # 构建filter_complex
        filter_parts = []

        # 为每个时间戳创建延迟效果
        for i, timestamp in enumerate(timestamps):
            filter_parts.append(f'[{i}:a]volume=2.0,adelay={int(timestamp * 1000)}|{int(timestamp * 1000)}[delayed{i}]')

        # 混合所有延迟的音频
        inputs = [f'[delayed{i}]' for i in range(len(timestamps))]
        inputs.append(f'[{len(timestamps)}:a]')  # 添加静音轨道
        filter_parts.append(f'{"".join(inputs)}amix=inputs={len(inputs)}:duration=longest,volume=3.0[out]')

        filter_complex = ';'.join(filter_parts)

        cmd.extend(['-filter_complex', filter_complex])
        cmd.extend(['-map', '[out]'])
        cmd.extend(['-t', str(duration)])  # 设置总时长
        cmd.extend(['-c:a', 'libmp3lame', '-b:a', '192k'])  # 输出为MP3格式
        cmd.append(output_file)

        print(f"🎵 正在生成音效轨道...")
        print(f"📁 输入音效: {os.path.basename(audio_file)}")
        print(f"⏱️  时间戳数量: {len(timestamps)}")
        print(f"⏰ 总时长: {duration:.1f}秒")

        # 执行FFmpeg命令
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print(f"✅ 音效轨道已生成: {output_file}")
            return True
        else:
            print(f"❌ FFmpeg错误: {result.stderr}")
            return False

    except Exception as e:
        print(f"❌ 生成音效轨道时出错: {e}")
        return False


def main():
    """主函数"""
    if len(sys.argv) < 4 or sys.argv[2] != '--audio':
        print("用法: python srt_processor.py input.srt --audio audio_file output.mp3")
        sys.exit(1)

    input_file = sys.argv[1]
    audio_file = sys.argv[3]
    output_file = sys.argv[4]

    try:
        # 读取SRT文件
        print(f"📖 读取文件: {input_file}")
        with open(input_file, 'r', encoding='utf-8') as f:
            srt_content = f.read()

        # 计算时间戳
        print("🔄 计算时间戳...")
        timestamps = calculate_timestamps(srt_content)

        if not timestamps:
            print("❌ 未找到任何被 `` 标记的词语")
            sys.exit(1)

        print(f"✅ 找到 {len(timestamps)} 个标记词语")

        # 生成音效轨道
        success = generate_audio_track(timestamps, audio_file, output_file)

        if success:
            print(f"🎉 音效轨道生成完成!")
            sys.exit(0)
        else:
            print("❌ 音效轨道生成失败")
            sys.exit(1)

    except FileNotFoundError:
        print(f"❌ 错误: 找不到文件 {input_file}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
