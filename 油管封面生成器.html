<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>催眠视频封面生成器</title>
    <style>
        /* 引入精选的 Google 字体 */
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&family=Playfair+Display:wght@700&family=Cinzel:wght@700&family=Lato:wght@300;400&display=swap');

        :root {
            --bg-color: #1a1d24;
            --panel-bg: #252a34;
            --text-color: #eaeaea;
            --accent-color: #7a9dff;
            --input-bg: #323846;
            --border-color: #4a4e5a;
        }

        body {
            font-family: 'Montserrat', 'Microsoft YaHei', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            margin: 0;
            padding: 20px;
            display: flex;
            gap: 20px;
            height: calc(100vh - 40px);
            overflow: hidden;
        }

        .container {
            display: flex;
            width: 100%;
            gap: 20px;
        }

        .controls {
            background-color: var(--panel-bg);
            padding: 25px;
            border-radius: 12px;
            width: 380px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .control-group label {
            font-weight: bold;
            font-size: 14px;
            color: var(--accent-color);
        }

        .control-group input[type="text"],
        .control-group select {
            width: 100%;
            padding: 12px;
            background-color: var(--input-bg);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
            transition: border-color 0.3s, box-shadow 0.3s;
        }

        .control-group input[type="text"]:focus,
        .control-group select:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(122, 157, 255, 0.3);
        }

        .checkbox-group label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 14px;
            font-weight: normal;
            color: var(--text-color);
        }
        
        .checkbox-group input[type="checkbox"] {
           margin-right: 10px;
           accent-color: var(--accent-color);
        }
        
        .logo-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .logo-controls input[type="file"] {
            display: none;
        }

        .logo-controls .file-label {
            background-color: var(--input-bg);
            color: var(--text-color);
            padding: 10px 15px;
            border-radius: 6px;
            cursor: pointer;
            text-align: center;
            flex-grow: 1;
            border: 1px dashed var(--border-color);
            transition: background-color 0.3s;
        }
        .logo-controls .file-label:hover {
            background-color: #41485a;
        }

        .preview-container {
            flex-grow: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: var(--panel-bg);
            border-radius: 12px;
            padding: 20px;
        }

        canvas {
            width: 100%;
            max-width: 960px; /* Limit max display size for very large screens */
            height: auto;
            aspect-ratio: 16 / 9;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        #export-btn {
            background: linear-gradient(45deg, var(--accent-color), #5f80ff);
            color: white;
            border: none;
            padding: 15px;
            font-size: 16px;
            font-weight: bold;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-top: auto; /* Push to the bottom */
        }

        #export-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(122, 157, 255, 0.4);
        }

        /* Responsive Design */
        @media (max-width: 900px) {
            body {
                flex-direction: column;
                height: auto;
            }
            .container {
                flex-direction: column;
            }
            .controls {
                width: 100%;
                box-sizing: border-box;
                max-height: 50vh;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="controls">
            <div class="control-group">
                <label for="main-title">主标题</label>
                <input type="text" id="main-title" value="DEEP SLEEP HYPNOSIS">
            </div>
            <div class="control-group">
                <label for="sub-title">副标题</label>
                <input type="text" id="sub-title" value="LET GO & SLEEP NOW">
            </div>
            <div class="control-group">
                <label for="video-info">视频信息</label>
                <input type="text" id="video-info" value="1 HOURS • DARK SCREEN">
            </div>
            <div class="control-group">
                <label for="author">作者</label>
                <input type="text" id="author" value="Sleepy Sheep Hypnosis">
            </div>
            <div class="control-group">
                <label for="bg-select">选择背景</label>
                <select id="bg-select">
                    <option value="https://images.unsplash.com/photo-1475274047050-1d0c0975c63e?q=80&w=2072&auto=format&fit=crop">深邃星空</option>
                    <option value="https://images.unsplash.com/photo-1506259091721-347e791bab0f?q=80&w=2070&auto=format&fit=crop">宁静海滩</option>
                    <option value="https://images.unsplash.com/photo-1536622543453-3a3a4a3a61b8?q=80&w=1974&auto=format&fit=crop">迷雾森林</option>
                    <option value="https://images.unsplash.com/photo-1519681393784-d120267933ba?q=80&w=2070&auto=format&fit=crop">雪山之巅</option>
                    <option value="https://images.unsplash.com/photo-1444703686981-a3abbc4d4fe3?q=80&w=2070&auto=format&fit=crop">银河</option>
                    <option value="#0d0f13">纯黑背景</option>
                </select>
            </div>
            <div class="control-group">
                <label for="font-select">选择字体</label>
                <select id="font-select">
                    <option value="Playfair Display">Playfair Display (优雅衬线)</option>
                    <option value="Cinzel">Cinzel (古典高级)</option>
                    <option value="Montserrat">Montserrat (现代无衬线)</option>
                    <option value="Lato">Lato (柔和简洁)</option>
                </select>
            </div>
            <div class="control-group">
                <label>Logo</label>
                <div class="logo-controls">
                    <label for="logo-upload" class="file-label">上传 Logo</label>
                    <input type="file" id="logo-upload" accept="image/*">
                    <select id="logo-position">
                        <option value="top-left">左上</option>
                        <option value="top-right">右上</option>
                        <option value="bottom-left">左下</option>
                        <option value="bottom-right">右下</option>
                    </select>
                </div>
            </div>
            <div class="control-group checkbox-group">
                <label><input type="checkbox" id="vignette-toggle" checked> 添加暗角</label>
                <label><input type="checkbox" id="glow-toggle"> 文字辉光</label>
                <label><input type="checkbox" id="shadow-toggle" checked> 文字阴影</label>
            </div>
            <button id="export-btn">导出封面 (1920x1080)</button>
        </div>
        <div class="preview-container">
            <canvas id="canvas" width="1920" height="1080"></canvas>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');

        // 获取所有控制元素
        const mainTitleInput = document.getElementById('main-title');
        const subTitleInput = document.getElementById('sub-title');
        const videoInfoInput = document.getElementById('video-info');
        const authorInput = document.getElementById('author');
        const bgSelect = document.getElementById('bg-select');
        const fontSelect = document.getElementById('font-select');
        const logoUpload = document.getElementById('logo-upload');
        const logoPositionSelect = document.getElementById('logo-position');
        const vignetteToggle = document.getElementById('vignette-toggle');
        const glowToggle = document.getElementById('glow-toggle');
        const shadowToggle = document.getElementById('shadow-toggle');
        const exportBtn = document.getElementById('export-btn');
        
        // 存储状态
        let bgImage = new Image();
        bgImage.crossOrigin = "Anonymous"; // 解决跨域问题
        let logoImage = null;

        // 核心绘制函数
        async function drawCanvas() {
            // 1. 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 2. 绘制背景
            const bgValue = bgSelect.value;
            if (bgValue.startsWith('#')) {
                // 纯色背景
                ctx.fillStyle = bgValue;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                await drawContent();
            } else {
                // 图片背景
                if (bgImage.src !== bgValue) {
                    bgImage.src = bgValue;
                    bgImage.onload = async () => {
                        ctx.drawImage(bgImage, 0, 0, canvas.width, canvas.height);
                        await drawContent();
                    };
                } else {
                    ctx.drawImage(bgImage, 0, 0, canvas.width, canvas.height);
                    await drawContent();
                }
            }
        }
        
        async function drawContent() {
             // 3. 绘制暗角 (可选)
            if (vignetteToggle.checked) {
                const gradient = ctx.createRadialGradient(canvas.width / 2, canvas.height / 2, canvas.height/2, canvas.width / 2, canvas.height / 2, canvas.width / 1.5);
                gradient.addColorStop(0, 'rgba(0,0,0,0)');
                gradient.addColorStop(1, 'rgba(0,0,0,0.7)');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
            }

            // 设置文字样式
            ctx.fillStyle = 'white';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            const selectedFont = fontSelect.value;
            
            // 设置文字特效
            if (shadowToggle.checked) {
                ctx.shadowColor = 'rgba(0, 0, 0, 0.7)';
                ctx.shadowBlur = 10;
                ctx.shadowOffsetX = 4;
                ctx.shadowOffsetY = 4;
            }
            if (glowToggle.checked) {
                ctx.shadowColor = 'rgba(255, 255, 255, 0.6)';
                ctx.shadowBlur = 20;
                ctx.shadowOffsetX = 0;
                ctx.shadowOffsetY = 0;
            }

            // 4. 绘制文字
            // 主标题 (大号)
            ctx.font = `bold 150px "${selectedFont}"`;
            ctx.fillText(mainTitleInput.value.toUpperCase(), canvas.width / 2, canvas.height / 2 - 80);

            // 副标题
            ctx.font = `400 60px "${selectedFont}"`;
            ctx.fillText(subTitleInput.value.toUpperCase(), canvas.width / 2, canvas.height / 2 + 70);
            
            // 清除文字特效，避免影响其他元素
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
            
            // 视频信息
            ctx.font = `300 36px "Lato"`; // 使用固定字体保持信息清晰
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.fillText(videoInfoInput.value.toUpperCase(), canvas.width / 2, canvas.height - 150);
            
            // 作者
            ctx.font = `400 32px "Lato"`;
            ctx.fillText(authorInput.value, canvas.width / 2, canvas.height - 80);

            // 5. 绘制 Logo (可选)
            if (logoImage) {
                const logoSize = 100; // Logo 显示尺寸
                const padding = 50; // 边距
                let x, y;
                switch (logoPositionSelect.value) {
                    case 'top-left':
                        x = padding; y = padding;
                        break;
                    case 'top-right':
                        x = canvas.width - logoSize - padding; y = padding;
                        break;
                    case 'bottom-left':
                        x = padding; y = canvas.height - logoSize - padding;
                        break;
                    case 'bottom-right':
                        x = canvas.width - logoSize - padding; y = canvas.height - logoSize - padding;
                        break;
                }
                ctx.drawImage(logoImage, x, y, logoSize, logoSize);
            }
        }

        // 事件监听
        const controls = document.querySelector('.controls');
        controls.addEventListener('input', drawCanvas);
        controls.addEventListener('change', drawCanvas);

        logoUpload.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (event) => {
                    logoImage = new Image();
                    logoImage.src = event.target.result;
                    logoImage.onload = drawCanvas;
                };
                reader.readAsDataURL(file);
            }
        });
        
        // 导出功能
        exportBtn.addEventListener('click', () => {
            const link = document.createElement('a');
            // 使用 toDataURL 导出 PNG 格式，以保证最佳清晰度
            link.download = `hypnosis-cover-${Date.now()}.png`;
            link.href = canvas.toDataURL('image/png');
            link.click();
        });

        // 初始绘制
        window.onload = drawCanvas;
    </script>
</body>
</html>